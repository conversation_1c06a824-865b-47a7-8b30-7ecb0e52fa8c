// import { useEffect, useState } from "react";
// import Vditor from "vditor";
// import "vditor/dist/index.css";
// import { v4 as uuidv4 } from 'uuid';
import { updateAnswerById } from "@/pages/config/components/aiResult/tools"
interface MdedtorProps {
    value: string; // 或者根据实际情况选择其他类型
    id: string,
}
// export default function mdedtor({ value, id }: MdedtorProps) {
//     console.log('mdedtor--->', value);
//     console.log(id)

//     const [vd, setVd] = useState<Vditor>();
//     useEffect(() => {
//         const vditor = new Vditor('vditor' + id, {
//             after: () => {
//                 vditor.setValue(value);
//                 setVd(vditor);
//             },
//             "mode": "sv",
//             "preview": {
//                 "mode": "both"
//             }
//         });
//         // Clear the effect
//         return () => {
//             vd?.destroy();
//             setVd(undefined);
//         };
//     }, []);
//     return <div id={"vditor" + id} className="vditor" />

// }


import { Editor, type EditorRef } from "@mini-markdown-rc/editor";
import { useEffect, useState, useRef } from "react";

export default function App({ value, id }: MdedtorProps) {
    const editorRef = useRef<EditorRef>(null);
    useEffect(() => {
        // console.log(value)
        editorRef.current?.clear()
        editorRef.current?.setContent(value)
    }, [value])

    const saveAnswer = (value: string, editorView: any) => {
        // console.log(id, value, editorView)
        updateAnswerById(id, value)
    }
    return <Editor
        toolbars={{
            excludeTools: ["help", "output", "emoji", "blockquote", "ul", "ol", "inlinecode", "code", "link", "image", "table", "undo", "redo", "write", "preview", "contents"],
        }}
        local={false}
        value={value}
        ref={editorRef}
        onSave={saveAnswer}

    />;
}