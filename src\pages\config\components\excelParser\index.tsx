import React, { useState } from 'react';
import { Upload, Button, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { read, utils } from 'xlsx';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

interface ExcelParserProps {
    onDataParsed: (data: any) => void;
}

const ExcelParser: React.FC<ExcelParserProps> = ({ onDataParsed }) => {
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    const handleUpload = async (file: File) => {
        try {
            const data = await file.arrayBuffer();
            const workbook = read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = utils.sheet_to_json(worksheet);

            onDataParsed(jsonData);
            message.success('Excel文件解析成功');
            return false; // 阻止自动上传
        } catch (error) {
            message.error('Excel文件解析失败');
            console.error('Excel解析错误:', error);
            return false;
        }
    };

    const uploadProps: UploadProps = {
        accept: '.xlsx,.xls',
        fileList,
        beforeUpload: handleUpload,
        onChange: ({ fileList }) => setFileList(fileList),
        maxCount: 1,
    };

    return (
        <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />}>选择Excel文件</Button>
        </Upload>
    );
};

export default ExcelParser;