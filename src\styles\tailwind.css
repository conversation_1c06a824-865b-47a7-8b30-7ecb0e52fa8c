/* 使用Tailwind的@apply指令来创建一个自定义的CSS类 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .order-detail-children {
    &>*:nth-child(odd) {
      @apply text-slate-600
    }

    &>*:nth-child(even) {
      @apply text-blue-400;
    }
  }

  .button-list {
    > :first-child {
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
    }

    > :last-child {
      border-bottom-left-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
    }
  }
}