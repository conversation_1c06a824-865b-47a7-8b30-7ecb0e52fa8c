import React, { useEffect, useState } from 'react'
import { FolderFilled, HddFilled, ShareAltOutlined, ProductFilled } from "@ant-design/icons";
import { Tooltip, Divider } from "antd";
import { XStream } from '@ant-design/x';
import { RichAssistantModeEnum } from '../placeholder/index'


export default function () {
    const [highlightedKey, setHighlightedKey] = useState<string>('');

    useEffect(() => {
        const handleHighlight = (event: CustomEvent) => {
            const key = event.detail.key;
            setHighlightedKey(key);
        };

        const handleClearHighlight = () => {
            setHighlightedKey('');
        };

        window.addEventListener('highlight-assistant', handleHighlight as EventListener);
        window.addEventListener('clear-assistant-highlight', handleClearHighlight);

        return () => {
            window.removeEventListener('highlight-assistant', handleHighlight as EventListener);
            window.removeEventListener('clear-assistant-highlight', handleClearHighlight);
        };
    }, []);

    const onclick = (id: string) => {
        const event = new CustomEvent('new-conversation', {
            detail: {
                id: id
            }
        });
        window.dispatchEvent(event) //派发事件
    }
    return (
        <div style={{ background: '#dae1ec', fontSize: 14 }} className='text-right p-3 pt-1  flex flex-col  bg-white w-[250px] shrink-0 '>
            {
                RichAssistantModeEnum.map(item => {
                    return (
                        <div key={item.key}>
                            <div className={`cursor-pointer hover:bg-[#cdd4de] p-2 rounded-lg flex justify-center ${highlightedKey === item.key ? 'bg-[#cdd4de] rounded-lg' : ''}`} onClick={() => { onclick(item.key) }}>
                                <Tooltip key={item.key} placement="left" title={item.description}  >
                                    <span className='mr-2 tracking-wide ' style={{ color: 'rgba(0,0,0,0.88)' }}>{item.label}</span>
                                    {/* <span className='rounded-full bg-gray-100 inline-block w-7 h-7 text-center leading-7'>{item.icon}</span> */}
                                </Tooltip>

                            </div>
                            <Divider className='m-0 w-[80%]' />
                        </div>

                    )
                })
            }
        </div>
    )
}
