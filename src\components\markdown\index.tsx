import { useEffect, useRef } from "react";
import Markdownit from 'markdown-it';
import sanitizer from 'markdown-it-sanitizer';
import container from 'markdown-it-container';
import texmath from "markdown-it-texmath";
import katex from "katex";


import 'katex/dist/katex.min.css'; // 引入 KaTeX 样式

import * as echarts from 'echarts';


import markdownItHighlightjs from 'markdown-it-highlightjs';
import { Typography } from 'antd';

import './index.css'

import hljs from "highlight.js/lib/core";
import "highlight.js/styles/ir-black.css";

import bash from 'highlight.js/lib/languages/bash'
import javascript from 'highlight.js/lib/languages/javascript';
import typescript from 'highlight.js/lib/languages/typescript';
import java from 'highlight.js/lib/languages/java';
import sql from 'highlight.js/lib/languages/sql';
import nginx from 'highlight.js/lib/languages/nginx';
import json from 'highlight.js/lib/languages/json';
import yaml from 'highlight.js/lib/languages/yaml';
import xml from 'highlight.js/lib/languages/xml';
// import htmlbars from 'highlight.js/lib/languages/htmlbars'
import shell from 'highlight.js/lib/languages/shell'

hljs.registerLanguage('bash', bash)
hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('typescript', typescript);
hljs.registerLanguage('java', java);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('nginx', nginx);
hljs.registerLanguage('json', json);
hljs.registerLanguage('yaml', yaml);
hljs.registerLanguage('xml', xml);
// hljs.registerLanguage('htmlbars', htmlbars);
hljs.registerLanguage('shell', shell);

interface MarkDownProps {
    content: string,
    type?: 'jiakuan' | 'other'
}

export default function Index({ content, type }: MarkDownProps) {
    const ref = useRef<HTMLDivElement>(null);
    const md = new Markdownit({
        html: true,
        linkify: true,
        breaks: true,
        xhtmlOut: true,
        typographer: true,
        langPrefix: 'language-',
    });
    md.use(markdownItHighlightjs, {
        hljs
    });
    md.use(texmath, { engine: katex, delimiters: ['dollars','brackets','doxygen','gitlab','julia','kramdown','beg_end'], katexOptions: { macros: {"\\RR": "\\mathbb{R}"} }});

    // md.use(sanitizer,  {
    //     removeUnbalanced: true,
    //     removeUnknown: true
    //   });//由于Markdown内容可能包含潜在的XSS攻击风险,该插件可以解决


    // md.use(container, 'warning', {
    //     validate: function (params: string) {
    //         console.log('validate-->', params);
    //         return params.trim().match(/^warning\s+(.*)$/);
    //     },
    //     render(tokens: any[], idx: number) {
    //         const m = tokens[idx].info.trim().match(/^warning\s+(.*)$/);

    //         if (tokens[idx].nesting === 1) {
    //             // 开始标签
    //             return `<div class="bg-red-500"><p>${md.utils.escapeHtml(m[1])}</p>\n`;
    //         } else {
    //             // 结束标签
    //             return '</div>\n';
    //         }
    //     }
    // });
    const defaultFenceRenderer = md.renderer.rules.fence || function (tokens, idx, options, env, self) {
        // 如果默认的fence渲染规则不存在，提供一个简单的回退逻辑
        const token = tokens[idx];
        // 使用 markdown-it 的 utils.escapeHtml 方法进行 HTML 转义
        return `<pre><code class="${token.info.trim()}">${md.utils.escapeHtml(token.content)}</code></pre>`;
    }; // 保存默认的fence渲染规则
    // 自定义代码块渲染
    md.renderer.rules.fence = (tokens, idx, options, env, self) => {
        const token = tokens[idx];
        // 自定义 ECharts 容器
        if (token.info.trim() === 'echarts') {
            try {
                console.log("token.content--->", token.content)
                // const config = JSON.parse(token.content);
                // const encodedConfig = encodeURIComponent(JSON.stringify(config));
                const encodedConfig = encodeURIComponent(token.content);
                return `<div class="echarts-container" data-config="${encodedConfig}" style="width: 100vw;max-width: 100%; aspect-ratio: 2"></div>`;
            } catch (e) {
                return `<div style="color: red">Invalid ECharts config: ${e}</div>`;
            }
        }
        return defaultFenceRenderer(tokens, idx, options, env, self);
    };
    useEffect(() => {
        if (!ref.current) return;
        const echartsDom = ref.current.querySelector(".echarts-container");
        // console.log('echarts-->', echarts);
        var config = echartsDom?.getAttribute('data-config')

        if (echartsDom) {
            if (config) {
                try {
                    const option = JSON.parse(decodeURIComponent(config))
                    var myChart = echarts.init(echartsDom as HTMLElement);
                    myChart.setOption(option);
                } catch (error) {
                    console.log(error)
                }
            }

        }
    }, [content])


    return (
        <div ref={ref} className={(type == 'jiakuan' && content.includes('\n')) ? 'bg-blue-50 px-3 py-2 rounded mt-2' : ''} dangerouslySetInnerHTML={{ __html: md.render(content) }} />
    )
}
