import {
    Attachments,
    Bubble,
    Conversations,
    Prompts,
    Sender,
    Welcome,
    useXAgent,
    useX<PERSON>hat,
    XStream,
    // BubbleProps,
    ThoughtChain
} from '@ant-design/x';
import type { ThoughtChainItem } from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { useEffect, useRef } from 'react';

import {
    CloudUploadOutlined,
    CheckCircleOutlined,
    InfoCircleOutlined,
    FireOutlined,
    LoadingOutlined,
    // PaperClipOutlined,
    PlusOutlined,
    // ReadOutlined,
    // ShareAltOutlined,
    // SmileOutlined,
    // MoreOutlined
    SmileOutlined
} from '@ant-design/icons';
// import {Badge} from 'antd'
import { Spin, Tag } from 'antd';
import { Button, type GetProp, type ButtonProps, Space, Tooltip } from 'antd';
import roboticon from '@/assets/robot.png'
import markdownit from 'markdown-it';
import { v4 as uuidv4 } from 'uuid';
import { useLocation } from 'react-router-dom';


function generateUniqueKey(): string {
    return uuidv4();
}
function getUserId(): any {
    let userId: string = localStorage.getItem('userId') || ''
    if (!userId) {
        userId = generateUniqueKey()
        localStorage.setItem('userId', userId)
    }
    return userId
}

const md = markdownit({ html: true, breaks: true, typographer: true });

const renderTitle = (icon: React.ReactElement, title: string) => (
    <Space align="start">
        {icon}
        <span>{title}</span>
    </Space>
);

const defaultConversationsItems = [
    {
        key: '0',
        label: '新会话',
    },
];

const useStyle = createStyles(({ token, css }) => {
    return {
        layout: css`
        width: 100%;
        min-width:500px;
        height: 100%;
        border-radius: ${token.borderRadius}px;
        display: flex;
        background: ${token.colorBgContainer};
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
        .ant-prompts {
          color: ${token.colorText};
        }
      `,
        menu: css`
        background: ${token.colorBgLayout}80;
        width: 280px;
        height: 100%;
        display: flex;
        flex-direction: column;
        display: none;
      `,
        conversations: css`
        padding: 0 12px;
        flex: 1;
        overflow-y: auto;
      `,
        chat: css`
        height: 100%;
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
        messages: css`
        flex: 1;
      `,
        placeholder: css`
        padding-top: 32px;
        width:100%;
      `,
        sender: css`
        box-shadow: ${token.boxShadow};
        display:none;
      `,
        logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;
  
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
        }
  
        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
        addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: calc(100% - 24px);
        margin: 12px 12px 12px 12px;
        
      `,
        welcome: css`
        width:100%;
      `,
        user_bubble: css`
        background-color: #00ff00;
      `

    };
});

const placeholderPromptsItems: GetProp<typeof Prompts, 'items'> = [
    {
        key: '1',
        label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '你可能想问'),
        description: '小区质差情况',
        children: [
            {
                key: '1-1',
                description: `请帮我查询一下小区ID为24713986059的质差情况。`,
            },
            {
                key: '1-2',
                description: `请帮我查询一下小区ID为23932372105的质差情况。`,
            },
            {
                key: '1-3',
                description: `请帮我查询一下小区ID为24015081608的质差情况。`,
            },
        ],
    },
    // {
    //     key: '2',
    //     label: renderTitle(<ReadOutlined style={{ color: '#1890FF' }} />, 'Design Guide'),
    //     description: 'How to design a good product?',
    //     children: [
    //         {
    //             key: '2-1',
    //             icon: <HeartOutlined />,
    //             description: `Know the well`,
    //         },
    //         {
    //             key: '2-2',
    //             icon: <SmileOutlined />,
    //             description: `Set the AI role`,
    //         },
    //         {
    //             key: '2-3',
    //             icon: <CommentOutlined />,
    //             description: `Express the feeling`,
    //         },
    //     ],
    // },
];

const senderPromptsItems: GetProp<typeof Prompts, 'items'> = [
    // {
    //     key: '1',
    //     description: '帮我查下小区id为10001的质差情况',
    //     icon: <FireOutlined style={{ color: '#FF4D4F' }} />,
    // },
    // {
    //     key: '2',
    //     description: 'Design Guide',
    //     icon: <ReadOutlined style={{ color: '#1890FF' }} />,
    // },
];


const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
        placement: 'start',
        // typing: { step: 5, interval: 100 },
        styles: {
            content: {
                // borderRadius: 16,
                backgroundColor: 'rgba(240, 240, 240, 0.5)'
            },
        },
        avatar: { icon: <img src={roboticon} />, style: { background: 'transparent', width: 50, height: 50 } },
        loadingRender() {
            return <span>正在加载中</span>
        },
    },
    local: {
        placement: 'end',
        variant: 'shadow',
        styles: {
            content: {
                backgroundColor: '#016af6',
                color: 'white'
            }
        },
        avatar: { icon: <SmileOutlined></SmileOutlined>, style: { color: 'white' } }
    },
};


function getStatusIcon(status: ThoughtChainItem['status']) {
    switch (status) {
        case 'success':
            return <CheckCircleOutlined />;
        case 'error':
            return <InfoCircleOutlined />;
        case 'pending':
            return <LoadingOutlined />;
        default:
            return undefined;
    }
}

const renderMarkdown = (content = '') => {
    // console.log('dlck', content)
    if (!content) return ''
    return (
        <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
    );
}

const renderLinkList = (content: Array<string>) => {
    return (
        <div>
            {
                content.map((item, index) => {
                    return (
                        <Button key={index} type="dashed" >
                            {item}
                        </Button>
                    )
                })
            }
        </div>
    )

}

function countOccurrences(str: string, subStr: string) {
    const regex = new RegExp(subStr, 'g');
    const matches = str.match(regex);
    return matches ? matches.length : 0;
}

const renderFuck = (content = '', isCancelled = false) => {
    const RESULT_TAG = '<RESULT_SS>'
    const END_TAG = '<END_SS>'
    const TITLE_TAG = '<TITLE_SS>'
    const LINK_TAG = '<LINK_SS>'
    const REFERENCE_START = "<REFERENCE_START>"
    const REFERENCE_END = "</REFERENCE_END>"

    if (content.includes('loading')) {
        return <div style={{ color: '#016af6' }}>思考中<Spin style={{ marginLeft: '10px' }}></Spin></div>
    }

    if (content.includes(LINK_TAG)) {
        return renderLinkList(content.split('\n'))
    }

    if (content.includes(TITLE_TAG)) {
        const result = content.split(RESULT_TAG)[1] || ''
        const contenta = content.split(RESULT_TAG)[0]
        const contentbody = contenta.split(END_TAG).filter((i) => i)

        const items = contentbody.map((c: any, index: number) => {
            const title = c.split(TITLE_TAG)[0]
            const body = c.split(TITLE_TAG)[1]

            // const isLast = (index + 1) == contentbody.length;
            const endOrder = countOccurrences(contenta, END_TAG) == (index + 1)
            let status: ThoughtChainItem['status'] = 'success';

            if (!endOrder && !result) {
                status = isCancelled ? 'error' : 'pending';
            }
            if (index < countOccurrences(contenta, END_TAG)) {
                status = 'success'
            }

            // console.log(status)

            return {
                title: title,
                content: body ? renderMarkdown(body) : '',
                icon: getStatusIcon(status),
                status: status,
            }

        });
        return (
            <div>
                <ThoughtChain items={items} />
                <div style={{ padding: '5px', boxSizing: 'border-box' }}>{renderMarkdown(result)}</div>
            </div>
        );
    }

    if (content.includes(REFERENCE_START) || content.includes(REFERENCE_END)) {
        const mainContent = content.split(REFERENCE_START)[0] || ''
        const referenceContentList = content.split(REFERENCE_START)[1].split(REFERENCE_END)
        return (
            <div> {renderMarkdown(mainContent)}
                {referenceContentList.map((item) => {
                    return (<Tag color='processing' bordered={false}> {item.replace(REFERENCE_END, '')}</Tag >)
                }).filter((c) => c)
                }

            </div>
        )
    }

    if (!content) {
        return (
            <div>
                该问题返回结果为空，请换个问题。
            </div>
        );
    }

    return (
        <div>
            {renderMarkdown(content)}
        </div>
    )

}


const Independent: React.FC = () => {
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================
    const [headerOpen, setHeaderOpen] = React.useState(false);

    const [content, setContent] = React.useState('');

    const [conversationsItems, setConversationsItems] = React.useState(defaultConversationsItems);

    const [activeKey, setActiveKey] = React.useState(defaultConversationsItems[0].key);


    const [attachedFiles, setAttachedFiles] = React.useState<GetProp<typeof Attachments, 'items'>>(
        [],
    );

    const [isConversationLoading, setisConversationLoading] = React.useState(false)
    const isLoadingConversationRef = useRef(isConversationLoading);

    useEffect(() => {
        isLoadingConversationRef.current = isConversationLoading;
    }, [isConversationLoading]);

    useEffect(() => {
        document.title = "无线质差分析智能体"
    }, [])

    const abortRef = useRef(() => { });
    useEffect(() => {
        return () => {
            abortRef.current();
        };
    }, []);


    const user_id_Ref = useRef(uuidv4())
    const session_id_Ref = useRef(uuidv4())

    enum api_model {
        tencent_ds = "tc_ds", //公有云腾讯转发DS测试用
        siliconflow_ds = "slf_ds", //共有硅基流动转发DS测试用
        backend_tencent_ds = 'bk_tc_ds', //公有云腾讯转发DS2测试用
        backend_siliconflow_ds = 'bk_slf_ds', //公有云硅基流动DS2测试用
        pybackend_sxc = 'sxc_pybk'//随心测python后端
    }

    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);

    const query_api_mode = queryParams.get('mode') || api_model.pybackend_sxc


    // Add a map to track cancelled status for each message
    const [cancelledMessages, setCancelledMessages] = React.useState<Record<string, boolean>>({});

    // ==================== Runtime ====================
    const [agent] = useXAgent({
        request: async ({ message }, { onSuccess, onUpdate }) => {
            const lo = true;
            setisConversationLoading(lo)
            console.log(isConversationLoading)
            onUpdate('loading')

            let controller = new AbortController();
            abortRef.current = () => {
                controller.abort('User cancelled the request');
                setisConversationLoading(false)
                onSuccess('停止请求')
            };

            let requestConfig: any;

            if (query_api_mode == api_model.pybackend_sxc) {
                requestConfig = {
                    url: import.meta.env.VITE_APP_API_URL + '/sxc-agent/multi_question/stream',
                    options: {
                        signal: controller.signal,
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ question: message, 'user_id': user_id_Ref.current, 'session_id': session_id_Ref.current })
                    }
                }
            } else if (query_api_mode == api_model.siliconflow_ds) {
                requestConfig = {
                    url: 'https://api.siliconflow.cn/v1/chat/completions',
                    options: {
                        method: 'POST',
                        headers: {
                            Authorization: '',
                            'Content-Type': 'application/json'
                        },
                        body: `{"model":"deepseek-ai/DeepSeek-V3","messages":[{"role":"user","content":"${message}"}],"stream":true}`
                    }
                }

            } else if (query_api_mode == api_model.tencent_ds) {
                requestConfig = {
                    url: 'https://wss.lke.cloud.tencent.com/v1/qbot/chat/sse',
                    options: {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            "content": message,
                            "bot_app_key": "",
                            "visitor_biz_id": getUserId(),
                            "session_id": session_id_Ref.current,
                        })
                    }
                }
            } else if (query_api_mode == api_model.backend_tencent_ds) {
                requestConfig = {
                    url: 'https://wss.lke.cloud.tencent.com/v1/qbot/chat/sse',
                    options: {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            "content": message,
                            "bot_app_key": "",
                            "visitor_biz_id": getUserId(),
                            "session_id": session_id_Ref.current,
                        })
                    }
                }
            }

            else {
                console.log('未配置模型')
                return
            }

            console.log(requestConfig)
            const res = await fetch(requestConfig.url, requestConfig.options)

            if (controller.signal.aborted) {
                console.log('Request was aborted:', controller.signal.reason);
                return;
            }

            if (res?.status != 200) {
                onSuccess('请确认您的网络环境是否正常，并重试。');
                setisConversationLoading(false)
                return
            }

            if (!res.body) {
                setisConversationLoading(false)
                return
            }

            const stream = XStream({
                readableStream: res.body
            });

            const reader = stream.getReader();
            abortRef.current = () => {
                reader?.cancel();
            };

            let chunks = [];


            while (reader) {

                let { value, done } = await reader.read();

                if (value?.data.trim() == '[DONE]' || done) {
                    console.log('done了')
                    onSuccess(chunks.join(''));
                    setisConversationLoading(false)
                    break;
                }
                if (!value) continue;

                let chunk;
                const prechunk = JSON.parse(value.data)

                if (query_api_mode == api_model.pybackend_sxc) {
                    chunk = prechunk.output
                    chunks.push(chunk)
                } else if (query_api_mode == api_model.siliconflow_ds) {
                    chunk = prechunk.choices[0].delta.content
                    chunks.push(chunk)
                } else if ((query_api_mode == api_model.tencent_ds || query_api_mode == api_model.backend_tencent_ds) && (prechunk.payload.is_from_self == false || prechunk.type == 'reference')) {
                    if (prechunk.type == 'reply') {
                        console.log('reply')
                        chunk = prechunk.payload.content
                        chunks[0] = chunk
                    } else if (prechunk.type == "reference") {
                        console.log('reference')
                        chunks[1] = prechunk.payload.references.map((ref: any) => {
                            console.log(ref)
                            return "<REFERENCE_START>" + ref.doc_name + "</REFERENCE_END>"
                        }).join('')
                    } else {
                        console.log('啥都不是')
                    }

                    console.log(chunk)
                } else {
                    chunk = ''
                }
                if (chunk) {

                    onUpdate(chunks.join(''))
                    console.log('update了')
                }
            }
            setisConversationLoading(false)

        }
    });

    const { onRequest, messages, setMessages } = useXChat({
        agent,

    });

    useEffect(() => {
        if (activeKey !== undefined) {
            setMessages([]);
        }
    }, [activeKey]);

    // ==================== Event ====================
    const onSubmit = (nextContent: string) => {
        if (!nextContent) return;
        setisConversationLoading(true)
        onRequest(nextContent);
        setContent('');
    };

    const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
        onRequest(info.data.description as string);
    };

    const onAddConversation = () => {
        setConversationsItems([
            ...conversationsItems,
            {
                key: `${conversationsItems.length}`,
                label: `新会话 ${conversationsItems.length}`,
            },
        ]);
        setActiveKey(`${conversationsItems.length}`);
    };

    const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
        setActiveKey(key);
    };

    const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info: any) =>
        setAttachedFiles(info.fileList);

    const handleCancel = () => {

        const currentMessageId = messages[messages.length - 1]?.id;
        // console.log('handleCancel', currentMessageId, messages)
        if (currentMessageId) {
            setCancelledMessages(prev => ({ ...prev, [currentMessageId]: true }));
        }
        setisConversationLoading(false)

        abortRef.current();
    };

    // ==================== Nodes ====================


    const placeholderNode: React.FC = () => {
        return (
            <Space direction="vertical" size={16} className={styles.placeholder}>
                <Welcome
                    variant="borderless"
                    icon={<img src={roboticon} />}
                    title="您好，我是无线质差分析智能体"
                    description="请向我提问"
                // extra={
                //     <Space>
                //         <Button icon={<ShareAltOutlined />} />
                //         <Button icon={<EllipsisOutlined />} />
                //     </Space>
                // }
                />
                <Prompts
                    // title="你想知道？"
                    items={placeholderPromptsItems}
                    styles={{
                        list: {
                            width: '100%',
                        },
                        item: {
                            flex: 1,
                        },
                    }}
                    onItemClick={onPromptsItemClick}
                />
            </Space>
        )
    }

    const items = messages.map(({ id, message, status }) => {
        const isUser = status === 'local' ? true : false;
        return {
            key: id,
            role: isUser ? 'local' : 'ai',
            content: message,
            messageRender: isUser ?
                renderMarkdown :
                (content: string) => renderFuck(content, cancelledMessages[id] || false),
        }
    });


    // const attachmentsNode = (
    //     <Badge dot={attachedFiles.length > 0 && !headerOpen}>
    //         <Button type="text" icon={<PaperClipOutlined />} onClick={() => setHeaderOpen(!headerOpen)} />
    //     </Badge>
    // );

    const senderHeader = (
        <Sender.Header
            title="Attachments"
            open={headerOpen}
            onOpenChange={setHeaderOpen}
            styles={{
                content: {
                    padding: 0,
                },
            }}
        >
            <Attachments
                beforeUpload={() => false}
                items={attachedFiles}
                onChange={handleFileChange}
                placeholder={(type) =>
                    type === 'drop'
                        ? { title: 'Drop file here' }
                        : {
                            icon: <CloudUploadOutlined />,
                            title: 'Upload files',
                            description: 'Click or drag files to this area to upload',
                        }
                }
            />
        </Sender.Header>
    );

    const logoNode = (
        <div className={styles.logo}>
            <img
                src={roboticon}
                draggable={false}
                alt="logo"
            />
            <span>预留槽位</span>
        </div>
    );

    const conversationMenu = (
        <div className={styles.menu}>
            {/* 🌟 Logo */}
            {logoNode}
            {/* 🌟 添加会话 */}
            <Button
                onClick={onAddConversation}
                type="link"
                className={styles.addBtn}
                icon={<PlusOutlined />}
            >
                新建会话
            </Button>
            {/* 🌟 会话管理 */}
            <Conversations
                items={conversationsItems}
                className={styles.conversations}
                activeKey={activeKey}
                onActiveChange={onConversationClick}
            />
        </div>
    )

    const renderSend = (
        props: ButtonProps & { ignoreLoading?: boolean; placeholder?: string } = {},
    ) => {
        const { ignoreLoading, placeholder, ...btnProps } = props;

        return (
            <Sender
                value={content}
                onChange={setContent}
                loading={isConversationLoading}
                onSubmit={onSubmit}
                // placeholder={placeholder}
                onCancel={handleCancel}
                actions={(_, info) => {
                    const { SendButton, LoadingButton } = info.components;

                    if (!ignoreLoading && isConversationLoading) {
                        return (
                            <Tooltip title="停止输出">
                                <LoadingButton />
                            </Tooltip>
                        );
                    }

                    let node = <SendButton {...btnProps} />;

                    return node;
                }}
            />
        );
    };


    // ==================== Render =================
    return (
        <div className={styles.layout}>
            {conversationMenu}
            <div className={styles.chat}>

                {items.length > 0 ? '' : placeholderNode({})}
                {/* 🌟 消息列表 */}
                <Bubble.List
                    items={items}
                    roles={roles}
                    className={styles.messages}
                />
                {/* 🌟 提示词 */}
                <Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />
                {/* 🌟 输入框 */}
                <Sender
                    value={content}
                    header={senderHeader}
                    onSubmit={onSubmit}
                    onChange={setContent}
                    // prefix={attachmentsNode}
                    loading={isConversationLoading}
                    className={styles.sender}
                    onCancel={handleCancel}

                />
                {
                    renderSend()
                }
            </div>
        </div>
    );
};

export default Independent;