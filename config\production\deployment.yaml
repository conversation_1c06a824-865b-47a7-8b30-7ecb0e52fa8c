apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-chatui-demo
  labels:
    app: agent-chatui-demo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-chatui-demo
  template:
    metadata:
      labels:
        app: agent-chatui-demo
    spec:
      containers:
        - name: agent-chatui-demo
          image: harbor.dcos.ncmp.unicom.local/mec-prod/agent-chatui-demo:v1
          imagePullPolicy: Always
          ports:
            - containerPort: 8890
          env:
            - name: VERSION_CONTROL
              value: "#{Build.BuildNumber}#"
