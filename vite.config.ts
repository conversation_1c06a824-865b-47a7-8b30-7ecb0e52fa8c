import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'



// https://vite.dev/config/
export default ({ mode }: any) => {

  const env = loadEnv(mode, process.cwd());
  const apiBaseUrl = env.VITE_APP_API_URL;

  const config = {
    base: './',
    plugins: [react()],
    resolve: {
      alias: {
        // 使用 @ 替换src目录
        '@': path.resolve(__dirname, './src')
      }
    },
    server: {
      host: "0.0.0.0",
      open: "/config",
      proxy: {
        // 家宽智能体接口
        // '/esb': {
        //   target: 'http://*************:28888',
        //   changeOrigin: true,
        // },

        // 配置助手大模型接口
        [apiBaseUrl + '/config-bk']: {
          target: 'http://*************:10080',
          // target: 'https://dashscope.aliyuncs.com',
          changeOrigin: true,
          rewrite: (path: any) => path.replace(apiBaseUrl + '/config-bk', ''), // 重写路径
        },
        // 配置助手大模型接口VL
        // '/compatible-mode': {
        //   target: 'https://dashscope.aliyuncs.com',
        //   changeOrigin: true,
        // },
        // 配置助手RAG
        [apiBaseUrl + '/config-rag']: {
          target: 'http://************:10010',
          changeOrigin: true,
          rewrite: (path: any) => path.replace(apiBaseUrl + '/config-rag', ''), // 重写路径
        },
        // 数据分析 自有接口
        [apiBaseUrl + '/data-analysis-bk']: {
          // target: 'http://127.0.0.1:5000',
          target: 'http://172.24.131.216:9900',
          changeOrigin: true,
          rewrite: (path: any) => path.replace(apiBaseUrl + '/data-analysis-bk', ''), // 重写路径
        },
        // 数据分析 ChatGPT接口
        [apiBaseUrl + '/data-analysis-multimodal']: {
          target: 'http://*************:10080',
          changeOrigin: true,
          rewrite: (path: any) => path.replace(apiBaseUrl + '/data-analysis-multimodal', ''), // 重写路径
        },
        // 知识中心
        // '/llm': {
        //   target: 'http://172.24.131.216:9081',
        //   changeOrigin: true,
        // },
        //随心测py服务
        [apiBaseUrl + '/sxc-agent']: {
          // target: 'http://127.0.0.1:5000',
          target: 'http://172.24.131.216:9901',
          changeOrigin: true,
          rewrite: (path: any) => path.replace(apiBaseUrl + '/sxc-agent', ''), // 重写路径
        }
      }
    }

  }
  return defineConfig(config)
}

