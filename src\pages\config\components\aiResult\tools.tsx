const FAVOURITE_LIST = 'favourite_list'
const MESSAGE_HISTORY = 'message_history'
const CONVERSATION_MESSAGE = 'conversation_message'
import { v4 as uuidv4 } from 'uuid';

interface MessageHistoryItem {
    id: string;
    conversationId: string;
    question: string;
    answer: string;
    isFavorite: boolean;
}

// IndexedDB setup
let db: IDBDatabase | null = null;
const DB_NAME = 'chatui_database';
const DB_VERSION = 1;

// Initialize the database
const initDB = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
        if (db) {
            resolve(db);
            return;
        }

        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = (event) => {
            console.error('IndexedDB error:', event);
            reject('Error opening database');
        };

        request.onsuccess = (event) => {
            db = (event.target as IDBOpenDBRequest).result;
            resolve(db);
        };

        request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            if (!db.objectStoreNames.contains(CONVERSATION_MESSAGE)) {
                db.createObjectStore(CONVERSATION_MESSAGE, { keyPath: 'conversationId' });
            }
        };
    });
};


// Wrapper functions to maintain the same API
export const setRawMessageList = async (messageList: any, conversationId: string, isFavoriteUpdate = false) => {
    try {
        const db = await initDB();
        const transaction = db.transaction([CONVERSATION_MESSAGE], 'readwrite');
        const store = transaction.objectStore(CONVERSATION_MESSAGE);

        // Use put to add or update the record based on conversationId
        const request = store.put({ conversationId, messageList });

        return new Promise<void>((resolve, reject) => {
            request.onsuccess = () => {
                console.log('Successfully saved/updated conversation:', conversationId);
                if (isFavoriteUpdate) {
                    console.log('触发favouritesUpdated事件');
                    window.dispatchEvent(new CustomEvent('favouritesUpdated')); // 触发事件
                }
                resolve();
            };

            request.onerror = (event) => {
                console.error('Error saving conversation message:', event);
                reject('Error saving conversation message');
            };

            transaction.onerror = (event) => {
                console.error('Transaction error in setRawMessageList:', event);
                reject('Transaction error saving conversation message');
            };
        });

    } catch (error) {
        console.error('Error in setRawMessageList:', error);
    }
};


export const getFavouriteList = async () => {
    try {
        const db = await initDB();
        const transaction = db.transaction([CONVERSATION_MESSAGE], 'readonly');
        const store = transaction.objectStore(CONVERSATION_MESSAGE);
        const request = store.openCursor();
        const favourite_list: any[] = [];

        return new Promise<any[]>((resolve, reject) => {
            request.onsuccess = (event) => {
                const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
                // console.log(cursor)
                if (cursor) {
                    const conversation = cursor.value;

                    conversation.messageList.forEach((message: any) => {
                        if (message.message.isfavourite) {
                            const q = conversation.messageList.find((m: any) =>
                                m.message.qaid === message.message.qaid && m.message.role === 'local'
                            );
                            if (q) {
                                favourite_list.push({
                                    question: q.message.content,
                                    answer: message.message.content[1],
                                    qaid: message.message.qaid
                                });
                            }
                        }
                    });
                    cursor.continue();
                } else {
                    resolve(favourite_list);
                }
            };

            request.onerror = (event) => {
                console.error('获取收藏列表时出错:', event);
                reject('获取收藏列表失败');
            };
        });

    } catch (error) {
        console.error('getFavouriteList 函数出错:', error);
        return [];
    }
};

export const getAnswerByQaId = async (qaid: string) => {
    try {
        const db = await initDB();
        const transaction = db.transaction([CONVERSATION_MESSAGE], 'readonly');
        const store = transaction.objectStore(CONVERSATION_MESSAGE);
        const request = store.openCursor();

        return new Promise<string>((resolve, reject) => {
            request.onsuccess = (event) => {
                const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
                if (cursor) {
                    const conversation = cursor.value;
                    const message = conversation.messageList.find((m: any) =>
                        m.message.qaid === qaid && m.message.role === 'ai'
                    );

                    if (message) {
                        resolve(message.message.content[1]);
                        return;
                    }
                    cursor.continue();
                } else {
                    resolve(''); // 未找到匹配的回答
                }
            };

            request.onerror = (event) => {
                console.error('获取回答时出错:', event);
                reject('获取回答失败');
            };
        });

    } catch (error) {
        console.error('getAnswerByQaId 函数出错:', error);
        return '';
    }
};

export const updateAnswerById = async (qaid: string, answer: string) => {
    try {
        const db = await initDB();
        const transaction = db.transaction([CONVERSATION_MESSAGE], 'readwrite');
        const store = transaction.objectStore(CONVERSATION_MESSAGE);
        const request = store.openCursor();

        return new Promise<void>((resolve, reject) => {
            request.onsuccess = (event) => {
                const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
                if (cursor) {
                    const conversation = cursor.value;
                    let updated = false;

                    for (let message of conversation.messageList) {
                        if (message.message.qaid === qaid && message.message.role === 'ai') {
                            message.message.content[1] = answer;
                            updated = true;
                            const updateRequest = cursor.update(conversation);

                            updateRequest.onsuccess = () => {
                                console.log('回答已更新，qaid:', qaid);
                                resolve();
                            };

                            updateRequest.onerror = (event) => {
                                console.error('更新回答时出错:', event);
                                reject('更新回答失败');
                            };
                            break;
                        }
                    }

                    if (!updated) {
                        cursor.continue();
                    }
                } else {
                    resolve(); // 遍历完成
                }
            };

            request.onerror = (event) => {
                console.error('打开游标时出错:', event);
                reject('更新回答失败');
            };
        });
    } catch (error) {
        console.error('updateAnswerById 函数出错:', error);
    }
};

export const clearFavourites = async () => {
    try {
        const db = await initDB();
        const transaction = db.transaction([CONVERSATION_MESSAGE], 'readwrite');
        const store = transaction.objectStore(CONVERSATION_MESSAGE);
        const request = store.openCursor();

        return new Promise<void>((resolve, reject) => {
            request.onsuccess = (event) => {
                const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
                if (cursor) {
                    const conversation = cursor.value;
                    let needsUpdate = false;

                    for (let message of conversation.messageList) {
                        if (message.message.isfavourite) {
                            message.message.isfavourite = 0;
                            needsUpdate = true;
                        }
                    }

                    if (needsUpdate) {
                        const updateRequest = cursor.update(conversation);
                        updateRequest.onerror = (event) => {
                            console.error('清除收藏状态时出错:', event);
                        };
                    }
                    cursor.continue();
                } else {
                    console.log('所有收藏已清除');
                    resolve();
                }
            };

            request.onerror = (event) => {
                console.error('打开游标时出错:', event);
                reject('清除收藏失败');
            };

            transaction.oncomplete = () => {
                window.dispatchEvent(new CustomEvent('favouritesUpdated')); // 触发事件
                resolve();
            };
        });
    } catch (error) {
        console.error('clearFavourites 函数出错:', error);
    }
};