import { Spin, Button } from 'antd';
import { FilePdfTwoTone, ShareAltOutlined } from "@ant-design/icons";
import Markdown from "@/components/markdown";
import { SearchResult } from "../../response";
import useStyle from '../../styles';
export type AIResultContent = [SearchResult[], string, boolean?, string?, string?]
import { assistanModeEnum, RichAssistantModeEnum, AssistantModeTree } from '../placeholder';
interface AIResultProps {
    content: AIResultContent
    onFavouriteChange: (qaId: string, isFavourite: number) => void
    assistantMode?: string,
    multiAgentSubAssistantMode?: string,
    onNextAgentClick: (assistMode: string, answer: string) => void
}
import MessageTool from '../messagetool';


export default function Index({ content, onFavouriteChange, assistantMode, multiAgentSubAssistantMode, onNextAgentClick }: AIResultProps) {
    const { styles } = useStyle()
    let searchNode = <></>
    // 联网搜索源展示
    if (content[0] && content[0].length > 0) {
        searchNode = (
            <div className='mt-3'>
                <span className='font-bold mb-2'><FilePdfTwoTone className='mr-2' />参考文档</span>
                {
                    content[0].map(item => {
                        return <a key={item.index} href={item.url} target="_blank" className='block text-blue-600' dangerouslySetInnerHTML={{ __html: item.title }} ></a>
                    })
                }
            </div>
        )
    }
    // 内容展示
    if (content[1]) {
        if (content[1].includes('loading')) {
            return <div style={{ color: '#016af6' }}>思考中<Spin style={{ marginLeft: '10px' }}></Spin></div>
        }
        return (
            <details open>
                <summary className={['cursor-pointer font-bold text-blue-700 text-base select-none marker:text-gray-700', styles.summary_style].join(" ")}><span className='mr-2'>{content[2] ? "答案已生成" : "……答案生成中"}</span></summary>
                {searchNode}
                < Markdown content={content[1]} />
                {
                    assistantMode === assistanModeEnum.MULTI_AGENT ?
                        <div>

                            {
                                content[4] && AssistantModeTree[content[4]] && AssistantModeTree[content[4]].map((item: string) => {
                                    return <Button className='mr-1 mt-1' key={item} type='primary' onClick={() => onNextAgentClick(item || '', content[1])} >{RichAssistantModeEnum.find((i) => i.key === item)?.rawName}</Button>
                                })
                            }

                        </div>
                        : ''
                }
                {content[2] ? <MessageTool onFavoriteClick={(value: number) => { content[3] ? onFavouriteChange(content[3], value) : '' }} /> : ''}
            </details>
        );
    }
    return searchNode;
}
