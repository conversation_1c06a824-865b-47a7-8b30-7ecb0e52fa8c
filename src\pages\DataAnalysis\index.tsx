import {
    B<PERSON>ble,
    Sender,
    Welcome,
    useXAgent,
    useXChat,
    XRequest
} from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { useState, useRef, useEffect } from 'react';
import { type GetProp, GetRef, Space, Spin } from 'antd';
import RobotWelcome from "@/assets/robot_welcome.png";
import Robot from '@/assets/robot.png';
import { Response, Data } from "./response";

import Markdown from "@/components/markdown";
import Table from "./components/tableAndEcharts/table";
import Button from "./components/button";
import { v4 as uuidv4 } from 'uuid';


const useStyle = createStyles(({ token, css }) => {
    return {
        layout: css`
        width: 100%;
        /* min-width: 1000px;*/
        height: 100%; 
        border-radius: ${token.borderRadius}px;
        display: flex;
        background: #f2f2f2;
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
  
        .ant-prompts {
          color: ${token.colorText};
        }
      `,
        menu: css`
        background: ${token.colorBgLayout}80;
        width: 280px;
        height: 100%;
        display: flex;
        flex-direction: column;
      `,
        conversations: css`
        padding: 0 12px;
        flex: 1;
        overflow-y: auto;
      `,
        chat: css`
        height: 100%;
        width: 60%;
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
        messages: css`
        flex: 1;
      `,
        placeholder: css`
      `,
        sender: css`
        box-shadow: ${token.boxShadow};
      `,
        logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;
  
        img {
          width: 40px;
          height: 30px;
          display: inline-block;
        }
  
        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
        addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: calc(100% - 24px);
        margin: 0 12px 24px 12px;
      `, summary_style: css`
      text-align: left;
      direction: rtl;
      ::marker{
        direction: ltr;
        padding-left: 1rem;
      }
      `
    };
});

interface ModelData {
    title: string,
    desc: string,
    questionList: string[],
    rowList: string[]
}


const Independent: React.FC = () => {
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================
    const [content, setContent] = useState('');
    const [historyMeg, setHistoryMeg] = useState<string[]>([]);

    // ==================== State ====================
    useEffect(() => {
        document.title = "中国联通数据分析助手"
        setMessages([{
            status: "local",
            id: uuidv4(),
            message: {
                role: 'model',
                content: {
                    title: "网络数据分析助手",
                    desc: `网络数据分析助手可以提供从数据查找、生成SQL、图表可视化到数据洞察分析全流程的交互式智能协作支持，有效降低一线人员在网络运营场景下的数据分析技术门槛。 `,
                    rowList: [
                        "1、4G工参信息表（cfg_siteinfo_tdlte）：记录4G基站及小区相关信息的关键数据表",
                        "2、上海道路业务指标天级分析表（dim_road_day）：针对上海市不同路段的业务指标进行日度统计与分析的数据表",
                        "3、4G天级干扰小区表（disturb_cell_4g_d）：记录4G网络中各个小区在不同日期的干扰情况",
                        "4、4G天级性能小区表（performance_4g_data_cell_day）：记录4G网络中各个小区在天级别的性能数据"
                    ],
                    questionList: ["上海市1月1日至今青徐联_11小区的4G接入成功率和掉线率整体趋势如何", "上海市1月10日青徐联_11的4G接入成功率是多少", "想了解2025年1月3日至2025年1月10日，三兴大楼_31的干扰情况变化"],
                },
                system: ''
            }
        }])
    }, [])

    // ==================== Ref ====================
    const listRef = useRef<GetRef<typeof Bubble.List>>(null);

    // ==================== Runtime ====================
    const [agent] = useXAgent<AgentMessage>({
        request: async ({ message }, { onUpdate, onSuccess }) => {
            if (message?.role == 'local') {
                const megList: Response[] = []
                onUpdate({
                    role: 'ai',
                    content: 'onLoading'
                })
                await exampleRequest.create(
                    {
                        queryLst: [...historyMeg, message.content],
                    },
                    {
                        onSuccess: (messages: Response[]) => {
                            // console.log('onSuccess--->', messages);

                            if (messages && messages.length > 0) {
                                let resultData = ''
                                for (let index = 0; index < messages.length; index++) {
                                    const element: Response = messages[index];
                                    const content = (JSON.parse(element.data) as Data).content.slice(0, -3);
                                    if (content.startsWith('...query')) {
                                        historyMeg.push(content.replace("...query", ""))
                                    }

                                    if (content.startsWith('...echarts')) {
                                        resultData = content.replace("...echarts", "")
                                    }
                                    if (content.startsWith('...button')) {
                                        element.resultData = resultData
                                    }
                                }
                            }
                            onSuccess({
                                role: 'ai',
                                content: messages
                            })
                        },
                        onUpdate: (messages: Response) => {
                            // console.log('onUpdate--->', messages);
                            megList.push(messages)
                            onUpdate({
                                role: 'ai',
                                content: megList
                            })
                        },
                        onError: (error) => {

                            console.error('onError--->', error);
                            onSuccess({
                                role: 'ai',
                                content: 'onError--->' + error.message
                            })
                        },
                    },
                );

            } else {
                let str = ''
                onUpdate({
                    role: 'model',
                    content: 'onLoading'
                })
                await modelRequest.create(
                    {
                        messages: [
                            { role: 'system', content: message?.system },
                            { role: 'user', content: message?.content }],
                        stream: true,
                    },
                    {
                        onSuccess: (messages) => {
                            let fullMsg = ""
                            for (let index = 0; index < messages.length; index++) {
                                const element = messages[index];
                                if (element.data != ' [DONE]') {
                                    fullMsg += parseOpenAIData(element.data)
                                }
                            }
                            console.log('onSuccess', fullMsg);
                            onSuccess({
                                role: 'model',
                                content: fullMsg
                            })
                        },
                        onError: (error) => {

                            console.error('onError--->', error);
                            onSuccess({
                                role: 'model',
                                content: 'onError--->' + error.message
                            })
                        },
                        onUpdate: (msg) => {
                            if (msg.data != ' [DONE]') {
                                str += parseOpenAIData(msg.data)
                                onUpdate({
                                    role: "model",
                                    content: str
                                })
                            }

                            console.log('onUpdate', msg);
                        },
                    },
                );
            }


        },
    });

    const parseOpenAIData = (msg: string) => {
        const choices = JSON.parse(msg).choices
        if (choices && choices.length > 0) {
            return choices[0].delta.content == undefined ? '' : choices[0].delta.content
        }
        return ''
    }

    const [controller, setController] = useState<AbortController | null>(null)
    const exampleRequest = XRequest({
        baseURL: import.meta.env.VITE_APP_API_URL + '/data-analysis-bk' + "/process_paramSteam0207",
        fetch: async (baseURL, options) => {
            delete options?.headers
            if (controller) {
                controller.abort()
            }
            //没有的话就到这一步
            const sl = new AbortController()
            setController(() => sl)
            const response = await fetch(baseURL, {
                ...options,
                signal: sl.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            })
            return response
        }
    });

    const modelRequest = XRequest({
        baseURL: import.meta.env.VITE_APP_API_URL + "/data-analysis-multimodal" + "/v1/chat/completions",
        model: "gpt-4o",
        dangerouslyApiKey: "sk-A6sz1HQ6DpsIjwn_T4XPg8xxrgs-xV6PqXEHBtkYzb9phsK6tR-Vgrm7Bts"
    });

    type AgentMessage = {
        role: 'local' | 'ai' | 'model' | 'botton';
        system?: string,
        content: string | Response[] | ModelData;
    };
    const { onRequest, messages, setMessages } = useXChat<AgentMessage>({
        agent,
    });

    // ==================== Event ====================
    const onSubmit = (nextContent: string) => {
        if (!nextContent) return;
        onRequest({
            role: 'local',
            content: nextContent
        });
        setContent('');
        scrollToBottom()
    };



    // ==================== Nodes ====================
    const placeholderNode = (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon={<img src={RobotWelcome} alt='欢迎机器人' />}
                title="你好,我是数据分析助手"
                description="我能够帮助你做数据分析"
            />
        </Space>
    );

    const items: GetProp<typeof Bubble.List, 'items'> = messages.map(({ id, message, status }) => ({
        key: id,
        ...message
    }));

    const systemList: { [key: string]: string } = {
        "图表展示": `{数据（表头+内容）}
你是一位专业的数据分析师，能够对各类数据进行深入解读和分析。请分析上面的数据，包括以下步骤：
1）数据理解: 仔细审查提供的数据，识别变量、数据类型和缺失值。
2）探索性数据分析: 包括描述性统计、数据可视化和相关性分析，以了解数据的分布、趋势和模式。
3）结果解释与建议: 分析数据的结果，结合业务知识给出相关建议。

{数据（表头+内容）}
你需要分析上述数据，判断适合图形化展示的内容，给出基于echarts语法的展示语句，内容仅需要echarts的option 标准的 JSON 字符串,以\`\`\`echarts开头,以\`\`\`结尾,请根据数值调整纵轴范围以优化数值展示效果。返回内容仅包括这些。`,
        "数据分析": `{数据（表头+内容）}
你是一位专业的数据分析师，能够对各类数据进行深入解读和分析。请分析上面的数据，包括以下步骤：
1）数据理解: 仔细审查提供的数据，识别变量、数据类型和缺失值。
2）探索性数据分析: 包括描述性统计、数据可视化和相关性分析，以了解数据的分布、趋势和模式。
3）结果解释与建议: 分析数据的结果，结合业务知识给出相关建议。


{数据（表头+内容）}
你需要分析上述数据`
    }

    const typeToComponentMap = {
        'markdown': (item: Data) => <Markdown key={item.uuid} content={item.content} />,
        'table': (item: Data) => <Table key={item.uuid} content={item.content} />,
        'button': (item: Data) => <Button key={item.uuid} content={item.content} clickBtn={() => {
            onRequest({
                role: 'botton',
                system: systemList[item.content],
                content: item.resultData as string
            })
            scrollToBottom()
        }} />,
        // 可以继续添加其他类型
    };


    const getReactNode = (content: string | Response[]) => {
        // console.log('content-->', content);
        if (typeof content == 'string') {
            if (content === "onLoading") {
                return <Spin></Spin>
            }
            if (content.startsWith('onError--->')) {
                return <span>{content.replace('onError--->', '')}</span>
            }
        }
        let list = []
        try {
            for (let index = 0; index < content.length; index++) {
                const element: Response = content[index] as Response
                const data = JSON.parse(element.data) as Data
                data.content = data.content.slice(0, -3);
                data.resultData = element.resultData
                let component;
                if (data.content.startsWith("...sql")) {
                    data.content = data.content.replace("...sql", '```sql\n') + "\n```"
                    list.push(component = typeToComponentMap['markdown'](data))
                } else if (data.content.startsWith("...txt")) {
                    data.content = data.content.replace("...txt", '')
                    list.push(component = typeToComponentMap['markdown'](data))
                } else if (data.content.startsWith("...title")) {
                    data.content = `**<font size="+1">☑  ${data.content.replace("...title", '')}</font>**`
                    list.push(component = typeToComponentMap['markdown'](data))
                } else if (data.content.startsWith("...echarts")) {
                    data.content = data.content.replace("...echarts", '')
                    list.push(component = typeToComponentMap['table'](data))
                } else if (data.content.startsWith("...button")) {
                    data.content = data.content.replace("...button", '')
                    list.push(component = typeToComponentMap['button'](data))
                }
            }
            return list
        } catch (error) {
            return <span>{String(error)}</span>
        }

    }

    const getReactNodeModel = (content: string) => {
        if (typeof content == 'string') {
            if (content === "onLoading") {
                return <Spin></Spin>
            }
            if (content.startsWith('onError--->')) {
                return <span>{content.replace('onError--->', '')}</span>
            }
        }
        return <Markdown content={content} />
    }
    const modelRender = (data: ModelData | string): React.ReactNode => {
        if (typeof data == 'string') {
            if (data === "onLoading") {
                return <Spin></Spin>
            }
            if (data.startsWith('onError--->')) {
                return <span>{data.replace('onError--->', '')}</span>
            }
            return <Markdown content={data} />
        }
        return (
            <details open>
                <summary className={['cursor-pointer font-bold text-blue-700 text-base select-none marker:text-gray-700', styles.summary_style].join(" ")}><span className='mr-2 text-[20px]'>{data.title}</span></summary>
                <div className='mt-3 font-black'>{data.desc}</div>
                <div className='mt-1 font-black'>您可分析的数据表如下：</div>
                {
                    data.rowList && data.rowList.map((row) => {
                        return <p className='mt-1'>{modelRender(row)}</p>
                    })
                }
                <p className='text-blue-500 font-bold mt-3 mb-2'>您可以尝试提问，体验我的超能力:</p>
                {
                    data.questionList && data.questionList.map((item, index) => {
                        return <p className='underline cursor-pointer mb-1' key={index} onClick={() => {
                            onRequest({
                                role: 'local',
                                content: item
                            })
                        }}>{index + 1}、 {item}</p>
                    })
                }
            </details>

        )
    }

    const roles: GetProp<typeof Bubble.List, 'roles'> = {
        ai: {
            placement: 'start',
            // typing: false,
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
            messageRender: getReactNode
        },
        model: {
            placement: 'start',
            // typing: false,
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
            messageRender: modelRender
        },
        local: {
            placement: 'end',
            variant: 'shadow',
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
        },
        botton: {
            placement: 'end',
            variant: 'shadow',
            styles: {
                content: {
                    display: "none"
                },
            },
            messageRender: () => <></>
        },
    };

    /**
     * 滑动到底部
     */
    const scrollToBottom = () => {
        if (items && items.length > 0 && items[items.length - 1]) {
            let lastMsg = items[items.length - 1]
            listRef.current?.scrollTo({ key: lastMsg.key, block: 'nearest' })
        }
    }

    // ==================== Render =================
    return (
        <div className={styles.layout}>
            <div className={styles.chat}>

                {/* 🌟 消息列表 */}
                <Bubble.List
                    items={items.length > 0 ? items : [{
                        content: placeholderNode, variant: 'borderless', styles: {
                            content: {
                                margin: "0 auto"
                            }
                        }
                    }]}
                    roles={roles}
                    className={styles.messages}
                />
                {/* 🌟 输入框 */}
                <Sender
                    value={content}
                    onSubmit={onSubmit}
                    onChange={setContent}
                    loading={agent.isRequesting()}
                    className={styles.sender}
                    onCancel={() => controller?.abort()}
                />
            </div>
        </div>
    );
};

export default Independent;