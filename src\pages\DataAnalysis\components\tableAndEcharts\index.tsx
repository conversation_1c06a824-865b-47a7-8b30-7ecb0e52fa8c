
import { Tabs } from 'antd';
import type { TabsProps, TableProps } from 'antd';
import Table from "./table";
import Echarts from "./echarts";

interface TableAndEchartsProps {
  content: string
}
export default function Index({ content }: TableAndEchartsProps) {
  //["{'小区类型': '辅干扰小区', '干扰类型': '外部干扰', 'rip值': Decimal('-94.7333')}"]
  // console.log('TableAndEchartsProps--->', content);

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '表格',
      children: <Table content={content} />,
    },
    {
      key: '2',
      label: '图表',
      children: <Echarts />,
    }
  ];
  return (
    <Tabs defaultActiveKey="1" items={items} />
  )
}
