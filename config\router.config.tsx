import BasicLayout from '../src/layout/BasicLayout';
import QAWEB from '@/pages/qa-web'
import QAWEBs from '@/pages/qa-web/index_s'
import DataAnalysis from '@/pages/DataAnalysis/framedataA'
import Config from '@/pages/config/frame'
import SIMPLECHAIN from '@/pages/config/components/simplemindchain'
import { Navigate } from 'react-router-dom';
import Math from '@/pages/Math'

const KGTestbaseRouter = '/uat-new-graph/chatui/power-group-agent'
const KGProBaseRouter = '/CUCCAI-new-graph/chatui/power-group-agent'
const routes = [
  {
    path: '/',
    element: <BasicLayout />,
    children: [
      {
        path: '/qa-web',
        index: true,
        element: <QAWEB />
      }, {
        path: '/qa-webs',
        index: true,
        element: <QAWEBs />
      }, {
        path: '/dataA',
        index: true,
        element: <DataAnalysis />,
        title: '中国联通数据分析助手',
        state: { title: '1212' }
      }, {
        path: '/config',
        index: true,
        element: <Config />,
      }, {
        path: '/simple-chain',
        index: true,
        element: <SIMPLECHAIN />
      },
      {
        path: '/test/chain',
        element: <QAWEBs />
      },
      {
        path: '/math',
        element: <Math />
      }
    ]
  }
];

routes[0].children.forEach(item => {
  if (item.path) {
    routes[0].children.push({
      path: KGTestbaseRouter + item.path,
      // element: <Navigate to={item.path} replace />,
      element: item.element
    })
    routes[0].children.push({
      path: KGProBaseRouter + item.path,
      // element: <Navigate to={item.path} replace />,
      element: item.element
    })
  }
})

export default routes;