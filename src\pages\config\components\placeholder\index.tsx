import {
    useXChat,
    Welcome,
    Prompts,
    type PromptProps
} from '@ant-design/x';
import { Space } from 'antd';
import useStyle from '../../styles';
import RobotWelcome from '@/assets/robot_welcome.png'
import { FireFilled, FolderFilled, HddFilled, DatabaseFilled, ShareAltOutlined, ProductFilled, CodeFilled, CodeSandboxCircleFilled, SettingFilled } from "@ant-design/icons";
import { v4 as uuidv4 } from 'uuid';
import { ModelData } from "../../response";
import { AgentMessage } from "../../index";
import React, { useImperativeHandle, type ForwardedRef } from 'react';
interface MyPromptProps extends PromptProps {
    rawName?: string,
    modelData?: ModelData
}
export enum assistanModeEnum {
    CONFIG_RECOMMEND = '1',
    ASSISTANT_DEBUG = '2',
    TOPO_ANAYLSIS = '3',
    CONFIG_GENERATOR = '4',
    MULTI_AGENT = '5',
    NETWORK_SIMULATOR = '6',
    OTHER = '7'
}

export const AssistantModeTree: any = {
    [assistanModeEnum.TOPO_ANAYLSIS]: [assistanModeEnum.CONFIG_RECOMMEND, assistanModeEnum.CONFIG_GENERATOR],
    [assistanModeEnum.CONFIG_RECOMMEND]: [assistanModeEnum.CONFIG_GENERATOR],
    [assistanModeEnum.CONFIG_GENERATOR]: [assistanModeEnum.ASSISTANT_DEBUG],
    [assistanModeEnum.ASSISTANT_DEBUG]: [assistanModeEnum.NETWORK_SIMULATOR]
}

const renderTitle = (icon: React.ReactElement, title: string) => (
    <Space align="start">
        {icon}
        <span>{title}</span>
    </Space>
);

export const RichAssistantModeEnum: MyPromptProps[] = [
    {
        key: assistanModeEnum.CONFIG_RECOMMEND,
        rawName: '配置推荐助手',
        label: renderTitle(<CodeSandboxCircleFilled style={{ color: '#0B5FA5' }} />, '配置推荐助手'),
        description: '欢迎体验配置推荐助手，根据您的需求将为您生成配置方案，包括但不限于配置步骤、配置思路、配置数据等',
        modelData: {
            title: "您已选择配置推荐助手",
            desc: `欢迎使用配置推荐助手!针对网络设备配置提供了强大的方案生成能力，覆盖主流厂商(华为、华三、中兴)路由器、交换机、防火墙、负载共计四大设备类型，辅助生成配置思路、配置步骤以及配置文件等。
            使用建议:在输入问题前请选择厂商与设备类型，以帮助您生成更准确的推荐配置建议;`,
            questionList: ["H3C的CR16000系列设备，配置OSPF路由的方案是什么?", "Hua WEi防火墙在校园出口方案中的配置思路、配置步骤有哪些?", "请帮忙上网查一下思科CN6000系列交换机的启动SNMP的配置步骤和命令验证?"],
        }
    }, {
        key: assistanModeEnum.ASSISTANT_DEBUG,
        rawName: '辅助调测助手',
        label: renderTitle(<SettingFilled style={{ color: '#1890FF' }} />, '辅助调测助手'),
        description: '欢迎体验辅助调测助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题',
        modelData: {
            title: "您已选择辅助调测助手",
            desc: `欢迎体验辅助调测助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题`,
            questionList: ["华为AR1200-S，出现告警CMD *******.4.1.2011.*********.136hwSuperChangesuccesful，该怎么处理?", "华为Sx3交换机出现IP地址冲突，怎么解决?", "3C的S9900系列交换机，报错信息如下;“Failed to accelerate,[STRING].ACL[UINT32],The operationis notsupported, 请问是什么问题?"],
        }
    },
    {
        key: assistanModeEnum.TOPO_ANAYLSIS,
        rawName: '拓扑图分析助手',
        label: renderTitle(<ShareAltOutlined style={{ color: '#00AE68' }} />, '拓扑图分析助手'),
        description: '欢迎体验拓扑图分析助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题',
        modelData: {
            title: "您已选择拓扑图分析助手",
            desc: `欢迎体验拓扑图分析助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题`,
            questionList: ["华为AR1200-S，出现告警CMD *******.4.1.2011.*********.136hwSuperChangesuccesful，该怎么处理?", "华为Sx3交换机出现IP地址冲突，怎么解决?", "3C的S9900系列交换机，报错信息如下;“Failed to accelerate,[STRING].ACL[UINT32],The operationis notsupported, 请问是什么问题?"],
        }
    }, {
        key: assistanModeEnum.CONFIG_GENERATOR,
        rawName: '配置生成助手',
        label: renderTitle(<CodeFilled style={{ color: '#1D1AB2' }} />, '配置生成助手'),
        description: '欢迎体验配置生成助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题',
        modelData: {
            title: "您已选择配置生成助手",
            desc: `欢迎体验配置生成助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题`,
            questionList: ["华为AR1200-S，出现告警CMD *******.4.1.2011.*********.136hwSuperChangesuccesful，该怎么处理?", "华为Sx3交换机出现IP地址冲突，怎么解决?", "3C的S9900系列交换机，报错信息如下;“Failed to accelerate,[STRING].ACL[UINT32],The operationis notsupported, 请问是什么问题?"],
        }
    },
    {
        key: assistanModeEnum.MULTI_AGENT,
        rawName: 'MultiAgent全流程助手',
        label: renderTitle(<ProductFilled style={{ color: '#FF9400' }} />, 'MultiAgent全流程助手'),
        description: '欢迎体验MultiAgent全流程助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题',
        modelData: {
            title: "您已选择MultiAgent全流程助手",
            desc: `欢迎体验MultiAgent全流程助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题`,
            questionList: ["华为AR1200-S，出现告警CMD *******.4.1.2011.*********.136hwSuperChangesuccesful，该怎么处理?", "华为Sx3交换机出现IP地址冲突，怎么解决?", "3C的S9900系列交换机，报错信息如下;“Failed to accelerate,[STRING].ACL[UINT32],The operationis notsupported, 请问是什么问题?"],
        }
    },
    {
        key: assistanModeEnum.NETWORK_SIMULATOR,
        rawName: '网络模拟器助手',
        label: renderTitle(<DatabaseFilled style={{ color: '#f38181' }} />, '网络模拟器助手'),
        description: '欢迎体验MultiAgent全流程助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题',
        modelData: {
            title: "您已选择MultiAgent全流程助手",
            desc: `欢迎体验MultiAgent全流程助手，帮助您理解和解决复杂配置问题的智能助手，以协助您快速定位并解决配置中的各种问题`,
            questionList: ["华为AR1200-S，出现告警CMD *******.4.1.2011.*********.136hwSuperChangesuccesful，该怎么处理?", "华为Sx3交换机出现IP地址冲突，怎么解决?", "3C的S9900系列交换机，报错信息如下;“Failed to accelerate,[STRING].ACL[UINT32],The operationis notsupported, 请问是什么问题?"],
        }
    },
];

interface PlaceholderPorps {
    setCurrentAssistantMode: React.Dispatch<React.SetStateAction<string>>,
    setMessages: ReturnType<typeof useXChat<AgentMessage>>['setMessages']
}


const Index = React.forwardRef(({ setCurrentAssistantMode, setMessages }: PlaceholderPorps, ref: ForwardedRef<{ onPromptsItemClick: (id: number) => void }>) => {
    const { styles } = useStyle()

    const onPromptsItemClick = (info: {
        data: MyPromptProps;
    }) => {
        setCurrentAssistantMode(info.data.key)
        setMessages(() => [{
            status: "local",
            id: uuidv4(),
            message: {
                role: 'model',
                content: info.data.modelData ?? "",
                isfavourite: 0
            }
        }])
    };
    // 使用useImperativeHandle自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        onPromptsItemClick(assistantmode: number) {
            onPromptsItemClick({
                data: RichAssistantModeEnum[assistantmode - 1]
            })
        }
    }));

    return (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon={<img src={RobotWelcome} />}
                title="您好，欢迎体验云池数通设备配置助手"
                description="您可以直接向我提问,或者选择应用场景助手后向我提问"
            />
            <Prompts
                title={renderTitle(<FireFilled style={{ color: '#FF4D4F' }} />, "热门助手推荐")}
                items={RichAssistantModeEnum}
                wrap
                styles={{
                    item: {
                        flex: 'none',
                        width: 'calc(33% - 6px)',
                    },
                }}
                onItemClick={onPromptsItemClick}
            />
        </Space>
    )
})


export default Index