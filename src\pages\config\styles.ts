import { createStyles } from 'antd-style';
const useStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
        width: 100%;
        min-width:500px;
        height: 100%;
        border-radius: ${token.borderRadius}px;
        display: flex;
        flex-direction: row;
        background: #f3f5fa;
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
        .ant-prompts {
          color: ${token.colorText};
        }
      `,
    menu: css`
        background: ${token.colorBgLayout}80;
        width: 18%;
        height: 100%;
        padding-top: 40px;
        display: flex;
        flex-direction: column;
      `,
    conversations: css`
        padding: 0 12px;
        flex: 1;
        overflow-y: auto;
      `,
    chat: css`
        height: 100%;
        width: 70%;
        margin: 0 auto;
        /* margin-left: 15%; */
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
    messages: css`
        flex: 1;
      `,
    placeholder: css`
        padding-top: 32px;
        width:100%;
      `,
    sender: css`
        box-shadow: ${token.boxShadow};
      `,
    logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;
  
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
        }
  
        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
    addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: fit-content;
        margin: 0 12px 24px 12px;
      `,
    welcome: css`
        width:100%;
      `,
    deatil: css`
    //  ▶
    `,
    summary_style: css`
      text-align: left;
      direction: rtl;
      ::marker{
        direction: ltr;
        padding-left: 1rem;
      }
    `,
    sender_container: css`
      background-color: white;
      box-shadow: 0px 0px 0px .5px #e6e9ec;
      border-radius: 24px;
      box-sizing: border-box;
      position: relative;

      .ant-sender-actions-list {
      position: absolute;
      right: 10px;
      bottom: 10px;
      }

      .ant-sender {
          position: initial;
      }

      >div {
          border: none !important;
          box-shadow: none !important;

      }

      .internet_button {
          height: 20px;
          margin-bottom: 10px;
          margin-left: 10px;
          border-radius: 20px;
      }
    `
  };
});
export default useStyle