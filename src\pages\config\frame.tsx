import React, { useEffect, useState, useRef } from 'react';
import { GetRef } from "antd";
import Config from './index'
import ConversationList from './conversation'
import './css.css'
import { v4 as uuidv4 } from 'uuid';
import Favourite from './components/favourite'
import MardDownEditor from './components/mardowneditor'
import AssistantList from './components/assistantList'
import { getAnswerByQaId } from './components/aiResult/tools.tsx'


const Frame = () => {
    const [activeKey, setActiveKey] = useState('')
    const [conversationsItems, setConversationsItems] = useState([])
    const [activeFavourite, setActiveFavourite] = useState('')
    const [favouriteItems, setFavouriteItems] = useState([])
    const [favouriteAnswer, setFavouriteAnswer] = useState('')
    const [currentFavoriteId, setCurrentFavoriteId] = useState('')
    const [currentFavoriteAnswer, setCurrentFavoriteAnswer] = useState('')
    const favoriteListRef = useRef<GetRef<typeof Favourite>>(null)
    const conversationListRef = useRef<GetRef<typeof ConversationList>>(null)
    // const currentFavoriteId = React.useRef('')

    // const setCurrentFavoriteId = (id: any) => {
    //     currentFavoriteId.current=id
    // }

    useEffect(() => {
        if (currentFavoriteId) {
            getAnswerByQaId(currentFavoriteId).then((answer) => {
                setCurrentFavoriteAnswer(answer)
            })
        }
    }, [currentFavoriteId])

    const conversationClick = (activeKey: any) => {
        setActiveKey(activeKey)
        setCurrentFavoriteId('')
        favoriteListRef?.current?.resetActiveKey()
    }

    const favouriteClick = (activeKey: any) => {
        setCurrentFavoriteId(activeKey)
        setActiveKey('')
        conversationListRef?.current?.resetActiveKey()
    }


    return (
        <div className='flex-column_container'>
            <div className='top_bar_container shrink-0 '>
                <img className='top_bar_logo margin-row' src="unicom.png" alt="" />
                <span className='top_bar_title'>云池数通设备配置助手</span>
                <img className='top_bar_user_avatar' src="robot.png" alt="" />
                <span className='top_bar_user_name margin-row'>用户</span>
            </div>

            <div className='flex-auto_container'>
                <div className='flex flex-col h-full w-[250px] '>
                    <div style={{ flex: '1 1 50%' }} className='overflow-y-hidden'>
                        <ConversationList ref={conversationListRef} setActiveKeyEvent={conversationClick} setConversationsItemsEvent={setConversationsItems} ></ConversationList>
                    </div>
                    <div className='overflow-y-auto' style={{ flex: '1 1 50%' }}>
                        <Favourite ref={favoriteListRef} setActiveKeyEvent={favouriteClick} setFavouriteItemsEvent={setFavouriteItems}></Favourite>
                    </div>
                </div>
                <div className='flex-auto_container '>
                    {
                        conversationsItems.map((item: any, index: number) => (

                            <Config key={item.key} conversationId={item.key} assistantMode={item.assistantmode} isActive={activeKey === item.key}  ></Config>

                        ))

                    }{
                        <div className={currentFavoriteId ? 'w-full' : 'hidden'}>
                            {
                                currentFavoriteAnswer ? <MardDownEditor id={currentFavoriteId} value={currentFavoriteAnswer} /> : ''
                            }
                        </div>
                    }

                </div>
                <AssistantList />
            </div>

        </div >
    )
}
export default Frame;