import React, { useEffect, useImperativeHandle, type ForwardedRef } from 'react';
import { type GetP<PERSON>, Button } from 'antd';
import { PlusOutlined, } from "@ant-design/icons";
import {
    Conversations
} from '@ant-design/x';
import './css.css'
import { CommentOutlined } from '@ant-design/icons';


import { Space, theme } from 'antd';
import { v4 as uuidv4 } from 'uuid';


const defaultConversationsItems = [
    {
        key: uuidv4(),
        label: '新会话',
        group: '今天',
        assistantmode: null
    },
    // {
    //     key: '1',
    //     label: '华三-路由器-配置需求xxxxx,请为我生成配置方案',
    //     group: '今天'
    // }
];

const ConversationList = React.forwardRef(({ setActiveKeyEvent, setConversationsItemsEvent }: any, ref: ForwardedRef<{ resetActiveKey: () => void }>) => {
    const [conversationsItems, setConversationsItems] = React.useState(defaultConversationsItems);
    const [activeKey, setActiveKey] = React.useState(defaultConversationsItems[0].key);

    useEffect(() => {
        setConversationsItemsEvent(conversationsItems)
    }, [conversationsItems])

    useEffect(() => {
        setActiveKeyEvent(activeKey)
    }, [])
    const onAddConversation = (assistantMode = null) => {
        const newId = uuidv4()
        setConversationsItems([
            ...conversationsItems,
            {
                key: newId,
                label: `新会话 ${conversationsItems.length}`,
                group: '今天',
                assistantmode: assistantMode
            },
        ]);
        setActiveKey(newId);
        setActiveKeyEvent(newId)

    };
    // 新增对话
    const handleNew = (event: CustomEvent) => {
        onAddConversation(event?.detail?.id)
    };
    useEffect(() => {
        //接受参数
        window.addEventListener('new-conversation', handleNew as EventListener)
        return () => {
            window.removeEventListener("new-conversation", handleNew as EventListener)
        }
    })

    // 使用useImperativeHandle自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        resetActiveKey() {
            setActiveKey('')
        }
    }));
    const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
        setActiveKey(key);
        setActiveKeyEvent(key);
    };

    const groupable: GetProp<typeof Conversations, 'groupable'> = {
        title: (group, { components: { GroupTitle } }) =>
            group ? (
                <GroupTitle>
                    <Space>
                        <CommentOutlined />
                        <span>{group}</span>
                    </Space>
                </GroupTitle>
            ) : (
                <GroupTitle />
            ),
    };

    return (
        <div className='menu'>
            {/* 🌟 添加会话 */}
            <Button
                onClick={() => { onAddConversation() }}
                type="link"
                className='addBtn'
                icon={<PlusOutlined />}
            >
                创建新对话
            </Button>
            {/* 🌟 会话管理 */}
            <Conversations
                groupable={groupable}
                items={conversationsItems}
                className="conversations"
                activeKey={activeKey}
                onActiveChange={onConversationClick}
            />
        </div>
    )
})

export default ConversationList;
