import React, { useState } from 'react';


import { createStyles } from 'antd-style';
const useStyle = createStyles(({ token, css }) => {
    return {
        select_button_container: css`
        
        .select-button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background-color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            font-size: 12px;
            box-sizing: border-box;
            padding: 15px 10px;
            color: #4c5166;
            display: inline-flex;
            align-items: center;
        }

        .select-button:hover {
            background-color: #e0e4ed;
        }

        .select-button.selected {
            background-color: #dbeafe;
            border-color: #c5defc;
            color: #4d6bfe;
        }

        .select-button.selected:hover {
            background-color: #c3daf8;

        }

        .select-button-icon {
            width: 15px;
            height: 15px;
            margin-right: 3px;
        }

        .select-button-icon.selected {
            filter: invert(50%) sepia(94%) saturate(4677%) hue-rotate(219deg) brightness(101%) contrast(99%);
        }
      `
    };
});


interface SelectButtonProps {
    text: string;
    onSelectionChange?: (selected: boolean) => void;
    className?: string;
}

const SelectButton: React.FC<SelectButtonProps> = ({
    text,
    onSelectionChange,
    className = ''
}) => {
    const [isSelected, setIsSelected] = useState(false);

    const { styles } = useStyle();
    const handleClick = () => {
        const newSelectedState = !isSelected;
        setIsSelected(newSelectedState);
        onSelectionChange?.(newSelectedState);
    };

    return (
        <div className={styles.select_button_container}>
            <div
                className={`select-button ${isSelected ? 'selected' : ''} ${className}`}
                onClick={handleClick}
            >
                <img className={`select-button-icon ${isSelected ? 'selected' : ''}`} src="network.svg" />
                {text}
            </div>
        </div>
    );
};

export default SelectButton; 