import { useEffect } from "react";
import { useRoutes, useLocation, useMatch } from 'react-router-dom';
import routes from '../config/router.config';

const App = () => {
    const route = useRoutes(routes)
    const { pathname } = useLocation();

    useEffect(() => {
        // console.log('location-->', useMatch(pathname));
        console.log('location-->', route);
    }, [pathname])
    return <>{route}</>
}
export default App;