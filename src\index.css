@import './styles/tailwind.css';

@font-face {
  font-family: 'PingFang SC-Medium';
  src: url('./assets/fonts/PingFangSC-Medium.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'PingFang SC-Regular';
  src: url('./assets/fonts/PingFangSC-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}





html,
body,
#root {
  width: 100%;
  height: 100%;
  -webkit-user-select: auto;
  /* Chrome, Safari, Opera */
  -moz-user-select: auto;
  /* Firefox */
  -ms-user-select: auto;
  /* IE 10+ */
  user-select: auto;
  /* Standard syntax */
}

/* 适用于Webkit浏览器（如Chrome和Safari） */
::-webkit-scrollbar {
  /* 滚动条宽度 */
  width: 0px;
}

html::-webkit-scrollbar-corner,
body::-webkit-scrollbar-corner {
  width: 0px;
}
