import '../../css.css'
import React, { useState } from 'react'

const page = () => {

    {/* <div className={(status == STATUS_ENUM.searching || status === STATUS_ENUM.generating || status == STATUS_ENUM.generated) ? 'show flex_row' : 'hidden'}>
                    <img className='inline mr-2' src="mind-node-answer-8c3e4738.svg" alt="" />
                    <span>正在搜索</span>
                </div> */}

    enum STATUS_ENUM {
        raging = 'raging',
        searching = 'searching',
        generating = 'generating',
        generated = 'generated'
    }

    const simpleMindChain: React.FC<any> = (status) => {

        // const [satusa, setStatusa] =React.useState('raging')
        console.log(status)
        return <div className='flex w-full '>

            <div className='p-3 flex-half box-border '>
                <div className='flex_row '>
                    <img className='inline mr-2' src="mind-node-analize-880eacbd.svg" alt="" />
                    <span>正在进行RAG</span></div>

                <div className={(status === STATUS_ENUM.generating || status == STATUS_ENUM.generated) ? 'show flex_row ' : 'hidden'}>
                    <img className='inline mr-2' src="mind-node-tool-267f8202.svg" alt="" />
                    <span>正在生成答案</span>
                </div>
                <div className={(status == STATUS_ENUM.generated) ? 'show flex_row ' : 'hidden'}>
                    <img style={{ marginLeft: -2 }} className='inline mr-1' src="mind-node-ready-a7c17706.svg" alt="" />
                    <span>答案生成完毕</span>
                </div>
            </div>
            <div className='flex-half  box-border relative'>
                <div className='absolute top-[25%] left-[40%] w-2xs h-2xs size-[30px] circle items-center justify-center flex '>
                    <img className='inline square' src="robot.png" alt="" />
                </div>
                <div className='absolute bottom-[25%] left-[25%] w-2xs h-2xs size-[30px] circle items-center justify-center flex'>
                    <img className='inline square' src="mind-node-analize-880eacbd.svg" alt="" />
                </div>
                <div className='absolute top-[25%] right-[25%] w-2xs h-2xs size-[30px] circle items-center justify-center flex'>
                    <img className='inline square' src="mind-node-tool-267f8202.svg" alt="" />

                </div>
                <div className='absolute bottom-[25%] right-[40%] w-2xs h-2xs size-[30px] circle items-center justify-center flex'>
                    <img style={{ marginLeft: -2 }} className='inline square' src="mind-node-ready-a7c17706.svg" alt="" />
                </div>
                <img className=' w-full h-full' src="graph-box-bg-cc898bfb.png" alt="" />
            </div>
        </div>
    }




    return (
        <div>
            {simpleMindChain('generated')}
        </div>
    )
}

export default page

