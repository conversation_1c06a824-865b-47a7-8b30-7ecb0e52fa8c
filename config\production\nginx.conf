#user  nobody;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    types {
        text/html                                        html htm shtml;
        text/css                                         css;
        text/xml                                         xml;
        image/gif                                        gif;
        image/jpeg                                       jpeg jpg;
        application/javascript                           js;
        application/atom+xml                             atom;
        application/rss+xml                              rss;

        text/mathml                                      mml;
        text/plain                                       txt;
        text/vnd.sun.j2me.app-descriptor                 jad;
        text/vnd.wap.wml                                 wml;
        text/x-component                                 htc;

        image/png                                        png;
        image/svg+xml                                    svg svgz;
        image/tiff                                       tif tiff;
        image/vnd.wap.wbmp                               wbmp;
        image/webp                                       webp;
        image/x-icon                                     ico;
        image/x-jng                                      jng;
        image/x-ms-bmp                                   bmp;

        font/woff                                        woff;
        font/woff2                                       woff2;

        application/java-archive                         jar war ear;
        application/json                                 json;
        application/mac-binhex40                         hqx;
        application/msword                               doc;
        application/pdf                                  pdf;
        application/postscript                           ps eps ai;
        application/rtf                                  rtf;
        application/vnd.apple.mpegurl                    m3u8;
        application/vnd.google-earth.kml+xml             kml;
        application/vnd.google-earth.kmz                 kmz;
        application/vnd.ms-excel                         xls;
        application/vnd.ms-fontobject                    eot;
        application/vnd.ms-powerpoint                    ppt;
        application/vnd.oasis.opendocument.graphics      odg;
        application/vnd.oasis.opendocument.presentation  odp;
        application/vnd.oasis.opendocument.spreadsheet   ods;
        application/vnd.oasis.opendocument.text          odt;
        application/vnd.openxmlformats-officedocument.presentationml.presentation
                                                        pptx;
        application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
                                                        xlsx;
        application/vnd.openxmlformats-officedocument.wordprocessingml.document
                                                        docx;
        application/vnd.wap.wmlc                         wmlc;
        application/x-7z-compressed                      7z;
        application/x-cocoa                              cco;
        application/x-java-archive-diff                  jardiff;
        application/x-java-jnlp-file                     jnlp;
        application/x-makeself                           run;
        application/x-perl                               pl pm;
        application/x-pilot                              prc pdb;
        application/x-rar-compressed                     rar;
        application/x-redhat-package-manager             rpm;
        application/x-sea                                sea;
        application/x-shockwave-flash                    swf;
        application/x-stuffit                            sit;
        application/x-tcl                                tcl tk;
        application/x-x509-ca-cert                       der pem crt;
        application/x-xpinstall                          xpi;
        application/xhtml+xml                            xhtml;
        application/xspf+xml                             xspf;
        application/zip                                  zip;

        application/octet-stream                         bin exe dll;
        application/octet-stream                         deb;
        application/octet-stream                         dmg;
        application/octet-stream                         iso img;
        application/octet-stream                         msi msp msm;

        audio/midi                                       mid midi kar;
        audio/mpeg                                       mp3;
        audio/ogg                                        ogg;
        audio/x-m4a                                      m4a;
        audio/x-realaudio                                ra;

        video/3gpp                                       3gpp 3gp;
        video/mp2t                                       ts;
        video/mp4                                        mp4;
        video/mpeg                                       mpeg mpg;
        video/quicktime                                  mov;
        video/webm                                       webm;
        video/x-flv                                      flv;
        video/x-m4v                                      m4v;
        video/x-mng                                      mng;
        video/x-ms-asf                                   asx asf;
        video/x-ms-wmv                                   wmv;
        video/x-msvideo                                  avi;
    }

    server_tokens off;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  120;
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    #大模型Python-agent
    # upstream big-model-agent-9900{
    #     server ************:9900;
    # }
    # server{
    #     listen 9900 so_keepalive=on;
    #     proxy_pass big-model-agent-9900;
    #     proxy_connect_timeout 1h;
    #     proxy_timeout 1h;
    # }



    server {
        listen       8890;
        server_name  swat-app-pro;
        root /usr/share/nginx/html;
        index  index.html index.htm;

        location / {
            try_files $uri $uri/ /index.html;
        }

        # 后端业务接口反向代理配置
        # location ~/esb/ {
        #     proxy_pass http://172.24.123.33:28888; # 后端端口地址
        #     proxy_connect_timeout 75;
        #     proxy_read_timeout 120s;
        #     proxy_send_timeout 120s;
        # }
         # 后端业务接口反向代理配置

        # 后端业务接口反向代理配置
        location ~* \/data-analysis-bk\/(.*)  {
            proxy_pass http://************:9900/$1$is_args$args; # 后端端口地址
            proxy_connect_timeout 75;
            proxy_read_timeout 6000s;
            proxy_send_timeout 6000s;
            proxy_redirect off;
            proxy_cache off;  # 关闭缓存，防止代理服务器缓存流式响应内容。
            proxy_buffering off;  # 关闭代理缓冲，防止缓冲整个响应后再发送给客户端。
            chunked_transfer_encoding on;  # 开启分块传输编码，允许将响应分成多个块进行传输。
            tcp_nopush on;  # 开启 TCP NOPUSH 选项，禁用 Nagle 算法，防止小块数据合并。
            tcp_nodelay on;  # 开启 TCP NODELAY 选项，禁用延迟 ACK 算法，确保及时发送。
            keepalive_timeout 300;  # 增加 keepalive 超时时间，防止连接在流式响应未完成时关闭。
            proxy_set_header Connection "";
            proxy_set_header   Host             $host;
            proxy_set_header   X-Real-IP        $remote_addr;
            proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        }

        

              # 后端业务接口反向代理配置
        location ~* \/config-bk\/(.*)  {
            proxy_pass http://*************:10080/$1$is_args$args; # 后端端口地址
            proxy_connect_timeout 75;
            proxy_read_timeout 6000s;
            proxy_send_timeout 6000s;
            proxy_redirect off;
            proxy_cache off;  # 关闭缓存，防止代理服务器缓存流式响应内容。
            proxy_buffering off;  # 关闭代理缓冲，防止缓冲整个响应后再发送给客户端。
            chunked_transfer_encoding on;  # 开启分块传输编码，允许将响应分成多个块进行传输。
            tcp_nopush on;  # 开启 TCP NOPUSH 选项，禁用 Nagle 算法，防止小块数据合并。
            tcp_nodelay on;  # 开启 TCP NODELAY 选项，禁用延迟 ACK 算法，确保及时发送。
            keepalive_timeout 300;  # 增加 keepalive 超时时间，防止连接在流式响应未完成时关闭。
            proxy_set_header Connection "";
            proxy_set_header   Host             $host;
            proxy_set_header   X-Real-IP        $remote_addr;
            proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        }

        # 后端业务接口反向代理配置
        location ~* \/data-analysis-multimodal\/(.*)  {
            proxy_pass http://*************:10080/$1$is_args$args; # 后端端口地址
            proxy_connect_timeout 75;
            proxy_read_timeout 6000s;
            proxy_send_timeout 6000s;
            proxy_cache off;  # 关闭缓存，防止代理服务器缓存流式响应内容。
            proxy_buffering off;  # 关闭代理缓冲，防止缓冲整个响应后再发送给客户端。
            chunked_transfer_encoding on;  # 开启分块传输编码，允许将响应分成多个块进行传输。
        }

        # 配置助手RAG
        location ~* \/config-rag\/(.*) {
            proxy_pass http://************:10010/$1$is_args$args; # 后端端口地址
            proxy_connect_timeout 75;
            proxy_read_timeout 6000s;
            proxy_send_timeout 6000s;
            proxy_cache off;  # 关闭缓存，防止代理服务器缓存流式响应内容。
            proxy_buffering off;  # 关闭代理缓冲，防止缓冲整个响应后再发送给客户端。
            chunked_transfer_encoding on;  # 开启分块传输编码，允许将响应分成多个块进行传输。
        }

        #随心测agent服务
        location ~* \/sxc-agent\/(.*) {
            #rewrite ^/power-group-agent-backend/sxc-agent/(.*)$ /$1 break;
            proxy_pass http://************:9901/$1$is_args$args;
            proxy_redirect off;
            proxy_buffering off; # 流式关闭响应缓存
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             # 增加代理读取和发送超时时间
            proxy_read_timeout 600;  # 设置为 300 秒（5 分钟）
            proxy_send_timeout 600;  # 设置为 300 秒（5 分钟）
        }

        # 错误页面配置，返回对应的页面路由地址
        error_page   404  /404;
        error_page   500 501 502 503 504  /50x;
        error_page   400 403 405 408 410 412 413 414 415  /otherError;
    }



}