import {
    <PERSON><PERSON><PERSON>,
    Sender,
    Welcome,
    useXA<PERSON>,
    useXChat,
    XRequest
} from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { useState, useRef, useEffect } from 'react';
import { type GetProp, GetRef, Space, Spin } from 'antd';
import RobotWelcome from "@/assets/robot_welcome.png";
import Robot from '@/assets/robot.png';

import Markdown from "@/components/markdown";

const useStyle = createStyles(({ token, css }) => {
    return {
        layout: css`
        width: 100%;
        /* min-width: 1000px;*/
        height: 100%; 
        border-radius: ${token.borderRadius}px;
        display: flex;
        background: #f2f2f2;
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
  
        .ant-prompts {
          color: ${token.colorText};
        }
        .ant-sender-content{
            flex-direction: column;
        }
      `,
        menu: css`
        background: ${token.colorBgLayout}80;
        width: 280px;
        height: 100%;
        display: flex;
        flex-direction: column;
      `,
        conversations: css`
        padding: 0 12px;
        flex: 1;
        overflow-y: auto;
      `,
        chat: css`
        height: 100%;
        width: 60%;
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
        messages: css`
        flex: 1;
      `,
        placeholder: css`
      `,
        sender: css`
        box-shadow: ${token.boxShadow};
      `,
        logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;
  
        img {
          width: 40px;
          height: 30px;
          display: inline-block;
        }
  
        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
        addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: calc(100% - 24px);
        margin: 0 12px 24px 12px;
      `,
    };
});


const Independent: React.FC = () => {
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================
    const [content, setContent] = useState('');
    const [historyMeg, setHistoryMeg] = useState<string[]>([]);

    // ==================== State ====================
    useEffect(() => {
        document.title = "中国联通数据分析助手"
    }, [])

    // ==================== Ref ====================
    const listRef = useRef<GetRef<typeof Bubble.List>>(null);

    // ==================== Runtime ====================
    const [agent] = useXAgent<AgentMessage>({
        request: async ({ message }, { onUpdate, onSuccess }) => {
            if (message?.role == 'local') {
                let megList: string = ""
                onUpdate({
                    role: 'ai',
                    content: 'onLoading'
                })
                await exampleRequest.create(
                    { "query": "时间相关的薛定谔方程及符号解释,(所有独占一行的块级公式display前加两个反斜杠n换行符)，内容仅包含这些", "userId": "zhouwj86", "conversationId": "conversation-22150", "modelTag": "", "stream": "1" } as Object,
                    {
                        onSuccess: (messages: Response[]) => {
                            console.log('onSuccess--->', megList);
                            onSuccess({
                                role: 'ai',
                                content: megList
                            })
                        },
                        onUpdate: (messages: Response) => {

                            megList += (JSON.parse(messages.data).answer as string)
                            onUpdate({
                                role: 'ai',
                                content: megList
                            })
                        },
                        onError: (error) => {

                            // console.error('onError--->', error);
                            // onSuccess({
                            //     role: 'ai',
                            //     content: 'onError--->' + error.message
                            // })
                        },
                    },
                );

            } else {
                let str = ''
                onUpdate({
                    role: 'model',
                    content: 'onLoading'
                })
                await modelRequest.create(
                    {
                        messages: [
                            { role: 'system', content: message?.system },
                            { role: 'user', content: message?.content }],
                        stream: true,
                    },
                    {
                        onSuccess: (messages) => {
                            let fullMsg = ""
                            for (let index = 0; index < messages.length; index++) {
                                const element = messages[index];
                                if (element.data != ' [DONE]') {
                                    fullMsg += parseOpenAIData(element.data)
                                }
                            }
                            console.log('onSuccess', fullMsg);
                            onSuccess({
                                role: 'model',
                                content: fullMsg
                            })
                        },
                        onError: (error) => {

                            console.error('onError--->', error);
                            onSuccess({
                                role: 'model',
                                content: 'onError--->' + error.message
                            })
                        },
                        onUpdate: (msg) => {
                            if (msg.data != ' [DONE]') {
                                str += parseOpenAIData(msg.data)
                                onUpdate({
                                    role: "model",
                                    content: str
                                })
                            }

                            console.log('onUpdate', msg);
                        },
                    },
                );
            }


        },
    });

    const parseOpenAIData = (msg: string) => {
        const choices = JSON.parse(msg).choices
        if (choices && choices.length > 0) {
            return choices[0].delta.content == undefined ? '' : choices[0].delta.content
        }
        return ''
    }

    const [controller, setController] = useState<AbortController | null>(null)
    const exampleRequest = XRequest({
        baseURL: "/llm/llm-service/chat/stream-v2",
        fetch: async (baseURL, options) => {
            delete options?.headers
            if (controller) {
                controller.abort()
            }
            //没有的话就到这一步
            const sl = new AbortController()
            setController(() => sl)
            const response = await fetch(baseURL, {
                ...options,
                signal: sl.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Authorization': 'Bearer 813ecf18-0a2d-4086-9d46-f71b130d9a7c',
                    'Accept': 'text/event-stream'
                }
            })
            return response
        }
    });

    const modelRequest = XRequest({
        baseURL: "/v1/chat/completions",
        model: "gpt-4o",
        dangerouslyApiKey: "sk-A6sz1HQ6DpsIjwn_T4XPg8xxrgs-xV6PqXEHBtkYzb9phsK6tR-Vgrm7Bts"
    });
    interface Response {
        data: string
    }

    type AgentMessage = {
        role: 'local' | 'ai' | 'model' | 'botton';
        system?: string,
        content: string | Response[];
    };
    const { onRequest, messages, setMessages } = useXChat<AgentMessage>({
        agent,
    });

    // ==================== Event ====================
    const onSubmit = (nextContent: string) => {
        if (!nextContent) return;
        onRequest({
            role: 'local',
            content: nextContent
        });
        setContent('');
        scrollToBottom()
    };



    // ==================== Nodes ====================
    const placeholderNode = (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon={<img src={RobotWelcome} alt='欢迎机器人' />}
                title="你好,我是数据分析助手"
                description="我能够帮助你做数据分析"
            />
        </Space>
    );

    const items: GetProp<typeof Bubble.List, 'items'> = messages.map(({ id, message, status }) => ({
        key: id,
        ...message
    }));





    const getReactNode = (content: string) => {
        // console.log('content-->', content);
        return <Markdown content={content}></Markdown>
    }

    const getReactNodeModel = (content: string) => {
        if (typeof content == 'string') {
            if (content === "onLoading") {
                return <Spin></Spin>
            }
            if (content.startsWith('onError--->')) {
                return <span>{content.replace('onError--->', '')}</span>
            }
        }
        return <Markdown content={content} />
    }

    const roles: GetProp<typeof Bubble.List, 'roles'> = {
        ai: {
            placement: 'start',
            // typing: false,
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
            messageRender: getReactNode
        },
        model: {
            placement: 'start',
            // typing: false,
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
            messageRender: getReactNodeModel
        },
        local: {
            placement: 'end',
            variant: 'shadow',
            styles: {
                content: {
                    borderRadius: 14,
                    background: "white"
                },
            },
        },
        botton: {
            placement: 'end',
            variant: 'shadow',
            styles: {
                content: {
                    display: "none"
                },
            },
            messageRender: () => <></>
        },
    };

    /**
     * 滑动到底部
     */
    const scrollToBottom = () => {
        if (items && items.length > 0 && items[items.length - 1]) {
            let lastMsg = items[items.length - 1]
            listRef.current?.scrollTo({ key: lastMsg.key, block: 'nearest' })
        }
    }
    // ==================== Render =================
    const math = `
**时间相关的薛定谔方程**  
方程形式：  

\\[ i\\hbar \\frac{\\partial}{\\partial t} \\Psi(\\mathbf{r}, t) = \\hat{H} \\Psi(\\mathbf{r}, t) \\]  
---

### **符号解释**  
1. **\\( i \\)**  
   - **意义**：虚数单位（满足 \\( i^2 = -1 \\)）。  
   - **作用**：体现量子力学中波函数的复数特性，是方程时间演化的必要数学工具。  

2. **\\( \\hbar \\)**  
   - **意义**：约化普朗克常数，\\( \\hbar = \\frac{h}{2\\pi} \\)，其中 \\( h \\) 为普朗克常数。  
   - **数值**：\\( \\hbar \\approx 1.0545718 \\times 10^{-34} \\, \\text{J·s} \\)。  
   - **作用**：量化量子系统的能量与时间演化尺度。  

3. **\\( \\frac{\\partial}{\\partial t} \\)**  
   - **意义**：对时间的偏导数。  
   - **作用**：描述波函数随时间的变化率。  

4. **\\( \\Psi(\\mathbf{r}, t) \\)**  
   - **意义**：波函数，量子系统的状态函数。  
   - **物理意义**：\\( |\\Psi(\\mathbf{r}, t)|^2 \\) 表示在位置 \\( \\mathbf{r} \\)、时间 \\( t \\) 处找到粒子的概率密度。  
   - **单位**：三维空间中的单位为 \\( \\text{m}^{-3/2} \\)。  

5. **\\( \\hat{H} \\)**  
   - **意义**：哈密顿算符，对应系统的总能量算符。  
   - **一般形式**：  

     \\[ \\hat{H} = -\\frac{\\hbar^2}{2m} \\nabla^2 + V(\\mathbf{r}, t) \\]  
     - \\( \\nabla^2 \\)：拉普拉斯算符（动能项），描述粒子运动的动能。  
     - \\( V(\\mathbf{r}, t) \\)：势能函数，描述粒子所处的外部势场。  

6. **\\( \\mathbf{r} \\)**  
   - **意义**：空间位置矢量（如三维坐标 \\( (x, y, z) \\)）。  
   - **作用**：波函数的空间变量，用于定位粒子在空间中的位置。  

7. **\\( t \\)**  
   - **意义**：时间变量。  
   - **作用**：标记量子系统演化的具体时刻。  

---
`
    // const math = `\\[ i\\hbar \\frac{\\partial}{\\partial t} \\Psi(\\mathbf{r}, t) = \\hat{H} \\Psi(\\mathbf{r}, t) \\]`;
    const math1 = `<think>好的，

    我现在需要容

    仅包含方程和符
    
    号解释，不</think>`;
    return (

        <div className={styles.layout}>

            <div className={styles.chat}>
                {/* <Markdown content={math}></Markdown> */}
                {/* 🌟 消息列表 */}
                <Bubble.List
                    items={items.length > 0 ? items : [{
                        content: placeholderNode, variant: 'borderless', styles: {
                            content: {
                                margin: "0 auto"
                            }
                        }
                    }]}
                    roles={roles}
                    className={styles.messages}
                />
                {/* 🌟 输入框 */}
                <Sender
                    value={content}
                    onSubmit={onSubmit}
                    onChange={setContent}
                    loading={agent.isRequesting()}
                    className={styles.sender}
                    onCancel={() => controller?.abort()}
                />
            </div>
        </div>
    );
};

export default Independent;
