import React from 'react'
import { But<PERSON> } from "antd";

interface ButtonProps {
    content: string,
    clickBtn: () => void
}
export default function Index({ content, clickBtn }: ButtonProps) {
    const buttonList: { [key: string]: string } = {
        "图表展示": "已生成结果查询表,请 ",
        "数据分析": "已生成结果查询表,请 "
    }
    const onClick = () => {
        clickBtn()
    }
    return (
        <div className='mt-2'>{buttonList[content]}  <Button size='small' type="primary" onClick={onClick}>{content}</Button></div>
    )
}
