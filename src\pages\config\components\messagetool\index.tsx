import React from 'react';
import { HeartOutlined } from '@ant-design/icons';
import { Flex, Rate } from 'antd';

const messagetool: React.FC<any> = ({ onFavoriteClick }) => {
    return (
        <div className='flex justify-end gap-2 box-border mt-1 '>
            <div className='inline-flex'>
                <Rate style={{ fontSize: 18 }} character="👍🏻" count={1} />
            </div>
            <div className='inline-flex'>
                <Rate style={{ fontSize: 18 }} character="👎🏻" count={1} />
            </div>
            <div className='inline-flex'>
                <Rate onChange={onFavoriteClick} count={1} />
            </div>
        </div>

    )
}

export default messagetool