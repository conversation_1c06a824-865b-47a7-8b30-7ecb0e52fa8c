import { useState, useRef, forwardRef, useImperative<PERSON><PERSON>le, Dispatch, SetStateAction } from "react";
import { Sender, Attachments } from "@ant-design/x";
import { CloudUploadOutlined } from "@ant-design/icons";
import { GetProp, GetRef, message, Upload, type UploadFile, UploadProps } from "antd";


interface UploadInstance {
    upload: (file: File) => void,
    btnClick: () => void
    clearFiles: () => void
}

type FileHandler = (file: File) => Promise<string | any>

interface UploadFileProps {
    onFileChange: (files: any[]) => void
    supportFormat: string[]
    handleFile: FileHandler
}
const Index = forwardRef<UploadInstance, UploadFileProps>((props, ref) => {

    // ==================== State ====================
    const [attachedFiles, setAttachedFiles] = useState<GetProp<typeof Attachments, 'items'>>([]);
    const handleFileChange: GetProp<typeof Attachments, 'onChange'> = async (info) => {
        const files: any[] = []
        for (const item of info.fileList) {
            const file = item.originFileObj as FileType;
            const result = await props.handleFile(file)
            files.push(result);
        }
        props.onFileChange(files);
        setAttachedFiles(info.fileList);
        const spanElement = attachmentsRef.current?.nativeElement?.querySelector('span.ant-upload-wrapper');
        if (spanElement instanceof HTMLElement) {
            spanElement.style.display = 'none';
        }
    }


    // ==================== useRef ====================
    const attachmentsRef = useRef<GetRef<typeof Attachments>>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

    const getBase64 = (file: FileType): Promise<string> =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = (error) => reject(error);
        });

    const beforeUpload = (file: FileType) => {
        const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
        console.log(fileExtension)
        const isValidFormat = props.supportFormat.includes(fileExtension);
        if (!isValidFormat) {
            message.error(`只允许上传 ${props.supportFormat.join('/')} 格式的文件!`);
        }
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            message.error('文件大小不能超过 10MB!');
        }
        return (isValidFormat && isLt10M) ? false : Upload.LIST_IGNORE;
    };
    // 使用 useImperativeHandle 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        upload(fileb: File) {
            attachmentsRef.current?.upload(fileb)
            attachmentsRef.current?.nativeElement?.click()
        },
        btnClick() {
            buttonRef.current?.click()
        },
        clearFiles() {
            setAttachedFiles(() => []);
            props.onFileChange([])
        }
    }));

    return (

        <Attachments
            beforeUpload={beforeUpload}
            ref={attachmentsRef}
            maxCount={1}
            items={attachedFiles}
            onChange={handleFileChange}
            placeholder={<button ref={buttonRef}>点击</button>}
            styles={{
                placeholder: {
                    display: 'none'
                },
                list: {
                    borderTopLeftRadius: '24px',
                    borderTopRightRadius: '24px'
                }
            }}
        >
        </Attachments>

    )
})
export default Index;