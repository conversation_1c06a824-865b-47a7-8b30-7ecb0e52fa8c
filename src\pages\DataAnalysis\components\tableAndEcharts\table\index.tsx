import { useEffect, useState } from 'react'
import { Table } from 'antd';
import { createStyles } from 'antd-style';

interface TableProps {
  content: string
}
const useStyle = createStyles(({ css }) => {
  return {
    customTable: css`
       .ant-table {
        .ant-table-container {
          .ant-table-body,
          .ant-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
      .ant-pagination{
          .ant-pagination-options{
            display: none;
          }
        }
    `,
  };
});
export default function Index({ content }: TableProps) {
  const [columns, setColumns] = useState<any[]>([])
  const [dataSource, setDataSource] = useState<any[]>([])
  useEffect(() => {
    try {
      let array: string[] = JSON.parse(content) as string[]
      const columnsTemp: any[] = []
      if (array.length > 0) {
        // console.log('array-->',array);
        let jsonStr: string = array[0].replace(/'/g, '"').replace('""', '"');
        const keys = Object.keys(JSON.parse(jsonStr));
        keys.forEach((key) => {
          console.log(key); // 输出键名：a、b、c
          columnsTemp.push(
            {
              title: key,
              dataIndex: key,
              key: key,
            })
        });
      }
      setColumns(() => [...columnsTemp])
      const dataSourceTemp: any[] = []
      for (let index = 0; index < array.length; index++) {
        let jsonStr = array[index].replace(/'/g, '"').replace('""', '"');
        const element = JSON.parse(jsonStr)
        dataSourceTemp.push({
          key: index,
          ...element
        })
      }
      setDataSource(() => [...dataSourceTemp])
    } catch (error) {
      console.error(error);
    }

  }, [])

  const { styles } = useStyle();
  return (
    <Table dataSource={dataSource} columns={columns} pagination={dataSource.length > 10 ? { pageSize: 10, total: dataSource.length } : false} scroll={{ y: 55 * 4 }} className={styles.customTable} />

  )
}
