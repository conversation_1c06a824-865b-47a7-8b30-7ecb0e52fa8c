{"name": "agent-chatui-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "devdlck": "vite --config vite.config.dlck.ts", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/x": "^1.0.4", "@mini-markdown-rc/editor": "^1.0.13", "@types/highlight.js": "^9.12.4", "@types/markdown-it-highlightjs": "^3.3.4", "@types/xlsx": "^0.0.35", "@xyflow/react": "^12.5.5", "antd-style": "^3.7.1", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "highlight.js": "^11.11.1", "katex": "^0.16.21", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-sanitizer": "^0.4.3", "markdown-it-texmath": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.1", "uuid": "^11.1.0", "vditor": "^3.10.9", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "@types/markdown-it-container": "^2.0.10", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "socks-proxy-agent": "^8.0.5", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}