import {
    Attachments,
    Bubble,
    Conversations,
    Prompts,
    Sender,
    Welcome,
    useXAgent,
    useX<PERSON><PERSON>,
    XStream,
    B<PERSON>bleProps
} from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { ReactNode, useEffect, useRef } from 'react';

import {
    CloudUploadOutlined,
    CommentOutlined,
    EllipsisOutlined,
    FireOutlined,
    HeartOutlined,
    PaperClipOutlined,
    PlusOutlined,
    ReadOutlined,
    ShareAltOutlined,
    SmileOutlined,
} from '@ant-design/icons';
import { Badge, Button, type GetProp, Space } from 'antd';
import roboticon from '@/assets/robot.png'
import markdownit from 'markdown-it';

const md = markdownit({ html: true, breaks: true });


const renderTitle = (icon: React.ReactElement, title: string) => (
    <Space align="start">
        {icon}
        <span>{title}</span>
    </Space>
);

const defaultConversationsItems = [
    {
        key: '0',
        label: 'What is Ant Design X?',
    },
];

const useStyle = createStyles(({ token, css }) => {
    return {
        layout: css`
        width: 100%;
        min-width:500px;
        height: 100%;
        border-radius: ${token.borderRadius}px;
        display: flex;
        background: ${token.colorBgContainer};
        font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
        .ant-prompts {
          color: ${token.colorText};
        }
      `,
        menu: css`
        background: ${token.colorBgLayout}80;
        width: 280px;
        height: 100%;
        display: flex;
        flex-direction: column;
      `,
        conversations: css`
        padding: 0 12px;
        flex: 1;
        overflow-y: auto;
      `,
        chat: css`
        height: 100%;
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: ${token.paddingLG}px;
        gap: 16px;
      `,
        messages: css`
        flex: 1;
      `,
        placeholder: css`
        padding-top: 32px;
        width:100%;
      `,
        sender: css`
        box-shadow: ${token.boxShadow};
      `,
        logo: css`
        display: flex;
        height: 72px;
        align-items: center;
        justify-content: start;
        padding: 0 24px;
        box-sizing: border-box;
  
        img {
          width: 24px;
          height: 24px;
          display: inline-block;
        }
  
        span {
          display: inline-block;
          margin: 0 8px;
          font-weight: bold;
          color: ${token.colorText};
          font-size: 16px;
        }
      `,
        addBtn: css`
        background: #1677ff0f;
        border: 1px solid #1677ff34;
        width: calc(100% - 24px);
        margin: 0 12px 24px 12px;
      `,
        welcome: css`
        width:100%;
      `,
        chat_div: css`
        // min-height:20px;
        
        
    
      `, chat_conatiner: css`
        display:flex;
        flex:auto;
        flex-direction:column;
        overflow-y:scroll;
        gap:20px;
        
      `
    };
});

const placeholderPromptsItems: GetProp<typeof Prompts, 'items'> = [
    {
        key: '1',
        label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '你可能想问'),
        description: '小区质差情况',
        children: [
            {
                key: '1-1',
                description: `请帮我查询一下小区ID为10001的质差情况`,
            },
            {
                key: '1-2',
                description: `请帮我查询一下小区ID为23001的质差情况?`,
            },
            {
                key: '1-3',
                description: `请帮我查询一下小区ID为33789的质差情况`,
            },
        ],
    },
    // {
    //     key: '2',
    //     label: renderTitle(<ReadOutlined style={{ color: '#1890FF' }} />, 'Design Guide'),
    //     description: 'How to design a good product?',
    //     children: [
    //         {
    //             key: '2-1',
    //             icon: <HeartOutlined />,
    //             description: `Know the well`,
    //         },
    //         {
    //             key: '2-2',
    //             icon: <SmileOutlined />,
    //             description: `Set the AI role`,
    //         },
    //         {
    //             key: '2-3',
    //             icon: <CommentOutlined />,
    //             description: `Express the feeling`,
    //         },
    //     ],
    // },
];

const senderPromptsItems: GetProp<typeof Prompts, 'items'> = [
    // {
    //     key: '1',
    //     description: '帮我查下小区id为10001的质差情况',
    //     icon: <FireOutlined style={{ color: '#FF4D4F' }} />,
    // },
    // {
    //     key: '2',
    //     description: 'Design Guide',
    //     icon: <ReadOutlined style={{ color: '#1890FF' }} />,
    // },
];




const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
        placement: 'start',
        typing: { step: 5, interval: 100 },
        styles: {
            content: {
                borderRadius: 16,
            },
        },
    },
    local: {
        placement: 'end',
        variant: 'shadow',
    },
};
const renderMarkdown: BubbleProps['messageRender'] = (content) => (

    <div>
        {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
        <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
    </div>

);

const Independent: React.FC = () => {
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================
    const [headerOpen, setHeaderOpen] = React.useState(false);

    const [content, setContent] = React.useState('');

    const [conversationsItems, setConversationsItems] = React.useState(defaultConversationsItems);

    const [activeKey, setActiveKey] = React.useState(defaultConversationsItems[0].key);

    const [attachedFiles, setAttachedFiles] = React.useState<GetProp<typeof Attachments, 'items'>>(
        [],
    );

    const abortRef = useRef(() => { });
    useEffect(() => {
        return () => {
            abortRef.current();
        };
    }, []);

    // ==================== Runtime ====================
    const [agent] = useXAgent({
        request: async ({ message }, { onSuccess, onUpdate, onError }) => {

            let res = await fetch('http://127.0.0.1:5000/multi_question/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ question: message })
            })
            if (!res.body) {
                onError({
                    name: 'error',
                    message: 'res.body为NULL'
                })
                console.error('res.body为NULL')
                return
            }
            const stream = XStream({
                readableStream: res.body
            });

            const reader = stream.getReader();
            abortRef.current = () => {
                reader?.cancel();
            };

            let current = '';

            while (reader) {
                console.log('read')
                let { value, done } = await reader.read();

                if (done) {
                    onSuccess(current);
                    break;
                }
                if (!value) continue;

                current += JSON.parse(value.data).output || '';
                onUpdate(current);
            }
        }
    });

    const { onRequest, messages, setMessages } = useXChat({
        agent,
    });

    useEffect(() => {
        if (activeKey !== undefined) {
            setMessages([]);
        }
    }, [activeKey]);

    // ==================== Event ====================
    const onSubmit = (nextContent: string) => {
        if (!nextContent) return;
        onRequest(nextContent);
        setContent('');
    };

    const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
        onRequest(info.data.description as string);
    };

    const onAddConversation = () => {
        setConversationsItems([
            ...conversationsItems,
            {
                key: `${conversationsItems.length}`,
                label: `New Conversation ${conversationsItems.length}`,
            },
        ]);
        setActiveKey(`${conversationsItems.length}`);
    };

    const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
        setActiveKey(key);
    };

    const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info: any) =>
        setAttachedFiles(info.fileList);

    // ==================== Nodes ====================


    const placeholderNode: React.FC = () => {
        return (
            <Space direction="vertical" size={16} className={styles.placeholder}>
                <Welcome
                    variant="borderless"
                    icon={<img src={roboticon} />}
                    title="您好，我是无线质差分析智能体"
                    description="请向我提问"
                // extra={
                //     <Space>
                //         <Button icon={<ShareAltOutlined />} />
                //         <Button icon={<EllipsisOutlined />} />
                //     </Space>
                // }
                />
                <Prompts
                    // title="你想知道？"
                    items={placeholderPromptsItems}
                    styles={{
                        list: {
                            width: '100%',
                        },
                        item: {
                            flex: 1,
                        },
                    }}
                    onItemClick={onPromptsItemClick}
                />
            </Space>
        )
    }

    const items: GetProp<typeof Bubble.List, 'items'> = messages.map(({ id, message, status }) => {

        return {
            key: id,
            // loading: status === 'loading',
            role: status === 'local' ? 'local' : 'ai',
            placement: status === 'local' ? 'end' : 'start',
            content: message,
            messageRender: renderMarkdown,

        }
    });


    const attachmentsNode = (
        <Badge dot={attachedFiles.length > 0 && !headerOpen}>
            <Button type="text" icon={<PaperClipOutlined />} onClick={() => setHeaderOpen(!headerOpen)} />
        </Badge>
    );

    const senderHeader = (
        <Sender.Header
            title="Attachments"
            open={headerOpen}
            onOpenChange={setHeaderOpen}
            styles={{
                content: {
                    padding: 0,
                },
            }}
        >
            <Attachments
                beforeUpload={() => false}
                items={attachedFiles}
                onChange={handleFileChange}
                placeholder={(type) =>
                    type === 'drop'
                        ? { title: 'Drop file here' }
                        : {
                            icon: <CloudUploadOutlined />,
                            title: 'Upload files',
                            description: 'Click or drag files to this area to upload',
                        }
                }
            />
        </Sender.Header>
    );

    const logoNode = (
        <div className={styles.logo}>
            <img
                src={roboticon}
                draggable={false}
                alt="logo"
            />
            <span>Ant Design X</span>
        </div>
    );

    // ==================== Render =================
    return (
        <div className={styles.layout}>
            <div className={styles.chat}>
                {items.length > 0 ? '' : placeholderNode({})}
                {/* 🌟 消息列表 */}
                {/* <Bubble.List
                    items={items}
                    roles={roles}
                    className={styles.messages}
                /> */}

                <div className={styles.chat_conatiner}>
                    {
                        items.map(element => {
                            console.log(element)
                            return <Bubble
                                key={element.key} content={element.content} placement={element.placement}>{element.content as ReactNode}

                            </Bubble>

                        })
                    }
                </div>


                {/* 🌟 提示词 */}
                <Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />
                {/* 🌟 输入框 */}
                <Sender
                    value={content}
                    header={senderHeader}
                    onSubmit={onSubmit}
                    onChange={setContent}
                    prefix={attachmentsNode}
                    loading={agent.isRequesting()}
                    className={styles.sender}
                    onCancel={() => abortRef.current()}
                />
            </div>
        </div>
    );
};

export default Independent;