import React, { useEffect, useRef, useImperativeHandle, type ForwardedRef } from 'react';
import { type GetProp, Button } from 'antd';
import { PlusOutlined, } from "@ant-design/icons";
import {
    Conversations
} from '@ant-design/x';
import '../../css.css'

import { clearFavourites, getFavouriteList } from '../../components/aiResult/tools'


const ConversationList = React.forwardRef(({ setActiveKeyEvent, setFavouriteItemsEvent }: any, ref: ForwardedRef<{ resetActiveKey: () => void }>) => {
    const [conversationsItems, setConversationsItems] = React.useState<any>([]);
    const [activeKey, setActiveKey] = React.useState('');
    const localRef = useRef<HTMLDivElement>(null);

    // 用于加载和更新收藏列表的函数
    const loadAndUpdateFavourites = async () => {
        const newFavouriteList = (await getFavouriteList()).map((item: any) => {
            return Object.assign(
                {
                    key: item.qaid,
                    label: item.question?.question || item.question,
                    style: {
                        marginLeft: '22px'
                    }
                }, item)
        });
        setConversationsItems(newFavouriteList);

    };

    // 监听自定义事件来更新收藏列表
    useEffect(() => {
        // 组件挂载时先加载一次
        loadAndUpdateFavourites();

        // 定义事件处理函数
        const handleFavouritesUpdate = () => {
            loadAndUpdateFavourites();
        };

        // 添加事件监听器
        window.addEventListener('favouritesUpdated', handleFavouritesUpdate);

        // 组件卸载时移除事件监听器
        return () => {
            window.removeEventListener('favouritesUpdated', handleFavouritesUpdate);
        };
    }, []); // 空依赖数组确保只在挂载和卸载时运行

    // 当 conversationsItems 更新时，通知父组件
    useEffect(() => {
        setFavouriteItemsEvent(conversationsItems)
    }, [conversationsItems, setFavouriteItemsEvent]) // 添加 setFavouriteItemsEvent 作为依赖

    // 使用useImperativeHandle自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        resetActiveKey() {
            setActiveKey('')
        }
    }));

    const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key: string) => {
        setActiveKey(key);
        setActiveKeyEvent(key);
    };

    // 清空收藏并触发更新
    const handleClearFavourites = async () => {
        await clearFavourites();
        // 手动触发一次更新，或者依赖 tools.tsx 中的事件触发
        // window.dispatchEvent(new CustomEvent('favouritesUpdated')); // 如果 clearFavourites 内部没有触发事件，则需要在这里触发
    };

    return (
        <div ref={localRef} className='menu'>
            {/* 🌟 收藏 */}
            <div className='inline-flex items-center p-[20px] pb-[12px] ' >
                <img className='size-[14px]' src="favorite.svg" alt="知识库" />
                <span className='ml-2 text-[13px] leading-[13px] '>知识库</span>
                {conversationsItems.length ? <img onClick={handleClearFavourites} className='size-[16px] ml-auto cursor-pointer ' src="delete.svg" alt="清空收藏" /> : ''}
            </div>
            <Conversations
                // groupable={groupable}
                items={conversationsItems}
                className="conversations"
                activeKey={activeKey}
                onActiveChange={onConversationClick}
                styles={{
                    item: {
                        paddingLeft: '30px'
                    },
                }}
            />
        </div>
    )
})


export default ConversationList;
